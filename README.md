# Trading Strategy Optimization

A simplified, working trading strategy optimization system for BTC/USDT.

## 🚀 Quick Start

### Step 1: Install Dependencies

**Option A: Automatic Installation (Recommended)**
```bash
python install.py
```

**Option B: Manual Installation**
```bash
pip uninstall pandas numpy -y
pip cache purge
pip install numpy==1.24.3 pandas==2.0.3 ta==0.10.2 ccxt==4.1.0 optuna==3.4.0 plotly==5.17.0 tqdm==4.66.1
```

**Option C: Using requirements.txt**
```bash
pip install -r requirements.txt
```

### Step 2: Test Installation
```bash
python test_simple.py
```

You should see:
```
✓ All packages imported successfully
✓ Basic pandas operations work
✓ Technical analysis indicators work
✓ Optuna optimization works
Results: 4/4 tests passed
```

### Step 3: Configure the Strategy (Optional)

The strategy uses `config.json` for all settings. Key configurations:

**Quick Test (Default)**:
```json
{
  "trading": {
    "use_test_mode": true,
    "test_timeframes": ["1h", "4h"]
  },
  "optimization": {
    "test_n_trials": 20
  }
}
```

**Full Optimization**:
```json
{
  "trading": {
    "use_test_mode": false,
    "timeframes": ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
  },
  "optimization": {
    "n_trials": 100
  }
}
```

### Step 4: Run the Trading Strategy

**Simple Version (Fast)**:
```bash
python o3_simple.py
```

**Enhanced Version (Full Features)**:
```bash
python o3.py
```

**Test Configuration System**:
```bash
python test_config.py
```

## 📁 Project Structure

```
├── o3_simple.py          # Main trading strategy (simplified)
├── o3.py                 # Enhanced version with full features
├── config.json           # Configuration file
├── config_manager.py     # Configuration management system
├── requirements.txt      # Dependencies
├── install.py           # Installation script
├── test_simple.py       # Simple tests
├── test_config.py       # Configuration system tests
├── README.md            # This file
└── results/             # Output directory (created automatically)
```

## ⚙️ Configuration System

The trading strategy uses a comprehensive configuration system via `config.json`:

### Key Configuration Sections:

#### **Trading Settings**
```json
{
  "trading": {
    "symbol": "BTC/USDT",
    "days_of_data": 90,
    "timeframes": ["1m", "5m", "15m", "30m", "1h", "4h", "1d"],
    "test_timeframes": ["1h", "4h"],
    "use_test_mode": true
  }
}
```

#### **Optimization Settings**
```json
{
  "optimization": {
    "n_trials": 100,
    "test_n_trials": 20,
    "n_startup_trials": 5,
    "enable_pruning": true
  }
}
```

#### **Parameter Ranges**
```json
{
  "parameter_ranges": {
    "macd": {
      "fast_min_base": 5,
      "fast_max_base": 24,
      "timeframe_scaling": {
        "enabled": true,
        "scale_power": 0.3
      }
    }
  }
}
```

#### **Scoring Weights**
```json
{
  "scoring": {
    "composite_weights": {
      "sharpe": 0.6,
      "return": 0.3,
      "winrate": 0.1
    }
  }
}
```

### Configuration Features:

- **🔄 Automatic Fallback**: Uses sensible defaults if config.json is missing
- **🎯 Test Mode**: Quick testing with reduced timeframes and trials
- **📏 Timeframe Scaling**: Automatically adjusts parameter ranges by timeframe
- **📊 Flexible Scoring**: Configurable weights for different performance metrics
- **🔧 Runtime Modification**: Can be modified programmatically during execution

## 🔧 Troubleshooting

### Binary Compatibility Error
If you see "numpy.dtype size changed" error:
```bash
python install.py
```

### Import Errors
```bash
pip uninstall pandas numpy scipy -y
pip install numpy==1.24.3 pandas==2.0.3
```

### Network/API Errors
- Check internet connection
- Binance API might be rate-limited (script handles this)

### Alternative: Use Conda
```bash
conda install pandas=2.0.3 numpy=1.24.3
pip install ta ccxt optuna plotly tqdm
```

## 📊 What the Strategy Does

1. **Fetches Data**: Downloads BTC/USDT price data from Binance
2. **Calculates Indicators**: MACD, RSI, DMI/ADX, ATR
3. **Generates Signals**: Long/short entry and exit signals
4. **Optimizes Parameters**: Uses Optuna to find best settings
5. **Evaluates Performance**: Calculates returns, Sharpe ratio, win rate
6. **Saves Results**: Stores optimization results in JSON format

## 🎯 Key Features

- **Simplified**: Minimal code, essential functionality only
- **Robust**: Handles errors gracefully
- **Fast**: Reduced trials and timeframes for quick testing
- **Compatible**: Fixed binary compatibility issues
- **Tested**: Includes verification tests

## 📈 Expected Output

```
Starting Trading Strategy Optimization
==================================================
Fetching data...
Fetching 1h data...
✓ Got 720 bars for 1h

Optimizing 1h...
[I 2025-01-16 17:30:00,000] A new study created...
100%|██████████| 50/50 [00:30<00:00,  1.67trial/s]
Best score: 1.2345
Best params: {'macd_fast': 12, 'macd_slow': 26, ...}
Test metrics: {'return': 0.15, 'sharpe': 1.23, 'winrate': 0.58}

Results saved to: results/optimization_results_20250116_173000.json
Optimization complete!
```

## 🔍 Next Steps

Once the basic version works:

1. **Extend timeframes**: Add more timeframes to `o3_simple.py`
2. **Increase trials**: Change `n_trials=50` to higher values
3. **Add visualization**: Uncomment plotting code
4. **Use full version**: Switch to `o3.py` for advanced features

## ⚠️ Important Notes

- **Internet required**: Script fetches live data from Binance
- **Rate limits**: Binance has API rate limits (handled automatically)
- **Paper trading**: This is for backtesting only, not live trading
- **Risk warning**: Past performance doesn't guarantee future results

## 🆘 Getting Help

If you still have issues:

1. **Check Python version**: `python --version` (need 3.8+)
2. **Check virtual environment**: Make sure you're in the right environment
3. **Try conda**: Often more reliable than pip for scientific packages
4. **Check logs**: Look for specific error messages

## ✅ Success Checklist

- [ ] `python test_simple.py` passes all tests
- [ ] `python o3_simple.py` runs without errors
- [ ] Results are saved to `results/` directory
- [ ] You see optimization progress bars
- [ ] Final metrics are displayed
