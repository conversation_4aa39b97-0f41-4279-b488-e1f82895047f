import sys
from typing import TYPE_CHECKING

if sys.version_info < (3, 7) or TYPE_CHECKING:
    from ._yref import <PERSON><PERSON><PERSON><PERSON><PERSON>da<PERSON>
    from ._yanchor import <PERSON><PERSON>Validator
    from ._y import <PERSON><PERSON><PERSON>da<PERSON>
    from ._xref import X<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._xanchor import Xan<PERSON>Valida<PERSON>
    from ._x import XValidator
    from ._text import Text<PERSON>alida<PERSON>
    from ._pad import PadValida<PERSON>
    from ._font import <PERSON>ontValidator
    from ._automargin import AutomarginValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._yref.YrefValidator",
            "._yanchor.YanchorValidator",
            "._y.YValidator",
            "._xref.XrefValidator",
            "._xanchor.XanchorValidator",
            "._x.XValidator",
            "._text.TextValidator",
            "._pad.PadValidator",
            "._font.FontValidator",
            "._automargin.AutomarginValidator",
        ],
    )
