#!/usr/bin/env python3
"""
Fix binary compatibility issues with pandas and numpy.
This script handles the common "numpy.dtype size changed" error.
"""

import subprocess
import sys
import os


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✓ Success")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False


def fix_binary_compatibility():
    """Fix binary compatibility issues between pandas and numpy."""
    print("Binary Compatibility Fix for pandas/numpy")
    print("=" * 50)
    
    # Step 1: Uninstall problematic packages
    packages_to_remove = ['pandas', 'numpy', 'scipy', 'matplotlib']
    
    for package in packages_to_remove:
        run_command([sys.executable, '-m', 'pip', 'uninstall', package, '-y'], 
                   f"Uninstalling {package}")
    
    # Step 2: Clear pip cache
    run_command([sys.executable, '-m', 'pip', 'cache', 'purge'], 
               "Clearing pip cache")
    
    # Step 3: Upgrade pip and setuptools
    run_command([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip', 'setuptools', 'wheel'], 
               "Upgrading pip and setuptools")
    
    # Step 4: Install numpy first (specific compatible version)
    run_command([sys.executable, '-m', 'pip', 'install', 'numpy==1.24.3'], 
               "Installing compatible numpy")
    
    # Step 5: Install pandas (compatible with numpy 1.24.3)
    run_command([sys.executable, '-m', 'pip', 'install', 'pandas==2.0.3'], 
               "Installing compatible pandas")
    
    # Step 6: Install other core packages
    other_packages = [
        'tqdm>=4.60.0',
        'plotly>=5.0.0',
        'pytest>=7.0.0'
    ]
    
    for package in other_packages:
        run_command([sys.executable, '-m', 'pip', 'install', package], 
                   f"Installing {package}")
    
    # Step 7: Install trading-specific packages
    trading_packages = [
        'ta>=0.10.0',
        'ccxt>=4.0.0',
        'optuna>=3.0.0'
    ]
    
    for package in trading_packages:
        run_command([sys.executable, '-m', 'pip', 'install', package], 
                   f"Installing {package}")


def test_installation():
    """Test if the installation works."""
    print("\n" + "=" * 50)
    print("Testing Installation...")
    
    test_imports = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('ta', None),
        ('ccxt', None),
        ('optuna', None),
        ('plotly', None),
        ('tqdm', None),
        ('pytest', None)
    ]
    
    failed_imports = []
    
    for module, alias in test_imports:
        try:
            if alias:
                exec(f"import {module} as {alias}")
            else:
                exec(f"import {module}")
            print(f"✓ {module}")
        except Exception as e:
            print(f"✗ {module}: {e}")
            failed_imports.append(module)
    
    if not failed_imports:
        print("\n✓ All packages imported successfully!")
        
        # Test pandas/numpy compatibility specifically
        try:
            import pandas as pd
            import numpy as np
            df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
            arr = np.array([1, 2, 3])
            print("✓ pandas/numpy compatibility test passed")
            return True
        except Exception as e:
            print(f"✗ pandas/numpy compatibility test failed: {e}")
            return False
    else:
        print(f"\n✗ Failed to import: {', '.join(failed_imports)}")
        return False


def main():
    """Main function."""
    print("Dependency Fix Script")
    print("This will fix binary compatibility issues between pandas and numpy")
    print("=" * 70)
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ Virtual environment detected")
    else:
        print("⚠ Warning: Not in a virtual environment")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Aborted. Please activate your virtual environment first.")
            return
    
    # Run the fix
    fix_binary_compatibility()
    
    # Test the installation
    success = test_installation()
    
    print("\n" + "=" * 70)
    if success:
        print("✓ SUCCESS: Dependencies fixed and tested!")
        print("\nYou can now run:")
        print("  python -m pytest test_o3.py -v")
        print("  python o3.py")
    else:
        print("✗ FAILED: Some issues remain")
        print("\nTry these alternatives:")
        print("1. Use conda instead:")
        print("   conda install pandas numpy")
        print("   pip install ta ccxt optuna")
        print("\n2. Use pre-compiled wheels:")
        print("   pip install --only-binary=all pandas numpy")
        print("\n3. Use specific versions:")
        print("   pip install pandas==1.5.3 numpy==1.24.3")


if __name__ == "__main__":
    main()
