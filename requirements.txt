# Trading Strategy Optimization Dependencies
# Core data manipulation and analysis
pandas>=1.5.0,<2.0.0
numpy>=1.21.0,<2.0.0

# Technical analysis
ta>=0.10.0,<1.0.0

# Cryptocurrency exchange API
ccxt>=3.0.0,<5.0.0

# Optimization
optuna>=3.0.0,<4.0.0

# Visualization
plotly>=5.0.0,<6.0.0

# Progress bars
tqdm>=4.60.0,<5.0.0

# Type checking (development)
mypy>=1.0.0,<2.0.0

# Testing (development)
pytest>=7.0.0,<8.0.0
pytest-cov>=4.0.0,<5.0.0

# Code formatting (development)
black
isort>=5.10.0,<6.0.0

# Linting (development)
flake8>=5.0.0,<7.0.0
