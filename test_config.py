#!/usr/bin/env python3
"""
Test script to demonstrate the configuration system.
"""

from config_manager import config_manager
import json


def test_configuration_system():
    """Test the configuration system functionality."""
    print("Configuration System Test")
    print("=" * 50)
    
    # Test 1: Basic configuration loading
    print("\n1. Configuration Loading:")
    print(f"✓ Config loaded from: config.json")
    print(f"✓ Symbol: {config_manager.get('trading.symbol')}")
    print(f"✓ Test mode: {config_manager.get('trading.use_test_mode')}")
    
    # Test 2: Timeframe selection
    print("\n2. Timeframe Selection:")
    print(f"✓ Available timeframes: {config_manager.get('trading.timeframes')}")
    print(f"✓ Test timeframes: {config_manager.get('trading.test_timeframes')}")
    print(f"✓ Active timeframes: {config_manager.get_timeframes()}")
    
    # Test 3: Trial count selection
    print("\n3. Trial Count Selection:")
    print(f"✓ Full trials: {config_manager.get('optimization.n_trials')}")
    print(f"✓ Test trials: {config_manager.get('optimization.test_n_trials')}")
    print(f"✓ Active trials: {config_manager.get_n_trials()}")
    
    # Test 4: Parameter ranges for different timeframes
    print("\n4. Parameter Ranges by Timeframe:")
    for tf in ["1h", "4h"]:
        ranges = config_manager.get_parameter_ranges_for_timeframe(tf)
        print(f"✓ {tf} timeframe:")
        print(f"  - MACD Fast: {ranges['macd']['fast_min']}-{ranges['macd']['fast_max']}")
        print(f"  - MACD Slow: {ranges['macd']['slow_min']}-{ranges['macd']['slow_max']}")
        print(f"  - RSI Length: {ranges['rsi']['len_min']}-{ranges['rsi']['len_max']}")
    
    # Test 5: Scoring configuration
    print("\n5. Scoring Configuration:")
    weights = config_manager.get('scoring.composite_weights')
    print(f"✓ Sharpe weight: {weights['sharpe']}")
    print(f"✓ Return weight: {weights['return']}")
    print(f"✓ Winrate weight: {weights['winrate']}")
    print(f"✓ Total weight: {sum(weights.values())}")
    
    # Test 6: Output configuration
    print("\n6. Output Configuration:")
    print(f"✓ Results directory: {config_manager.get('output.results_directory')}")
    print(f"✓ File prefix: {config_manager.get('output.file_prefix')}")
    print(f"✓ Include metadata: {config_manager.get('output.include_metadata')}")
    
    print("\n" + "=" * 50)
    print("✓ All configuration tests passed!")


def demonstrate_config_modification():
    """Demonstrate how to modify configuration programmatically."""
    print("\nConfiguration Modification Demo")
    print("=" * 50)
    
    # Show current settings
    print(f"Current test mode: {config_manager.get('trading.use_test_mode')}")
    print(f"Current trials: {config_manager.get_n_trials()}")
    
    # Modify configuration
    config_manager.config['trading']['use_test_mode'] = False
    print(f"Modified test mode: {config_manager.get('trading.use_test_mode')}")
    print(f"Modified trials: {config_manager.get_n_trials()}")
    
    # Reset for safety
    config_manager.config['trading']['use_test_mode'] = True
    print(f"Reset test mode: {config_manager.get('trading.use_test_mode')}")


def show_full_config():
    """Show the complete configuration structure."""
    print("\nComplete Configuration Structure")
    print("=" * 50)
    print(json.dumps(config_manager.config, indent=2))


if __name__ == "__main__":
    test_configuration_system()
    demonstrate_config_modification()
    
    # Uncomment to see full config
    # show_full_config()
