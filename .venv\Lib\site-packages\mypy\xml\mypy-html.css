/* CSS for type check coverage reports */

/*
    Used by both summary and file.
*/
body {
    font-family: "Helvetica Neue", sans-serif;
}

/*
    Used only by summary.
*/

h1 {
    text-align: center;
    font-size: 135%;
    margin: 20px;
}

table.summary {
    border-collapse: collapse;
    margin-left: 7%;
    margin-right: 7%;
    width: 85%;
}

table caption {
    margin: 1em;
}

table.summary, tr.summary, th.summary, td.summary {
    border: 1px solid #aaa;
}

th.summary, td.summary {
    padding: 0.4em;
}

td.summary a {
    text-decoration: none;
}

.summary-quality-0 {
    background-color: #dfd;
}

.summary-quality-1 {
    background-color: #ffa;
}

.summary-quality-2 {
    background-color: #faa;
}

td.summary-filename, th.summary-filename {
    text-align: left;
}

td.summary-filename {
    width: 50%;
}

.summary-precision {
    text-align: center;
}

.summary-lines {
    text-align: center;
}

/*
    Used only by file.
*/

td.table-lines {
    text-align: right;
    padding-right: 0.5em;
}

td.table-code { }

span.lineno {
    text-align: right;
}

a:link.lineno, a:visited.lineno {
    color: #999; text-decoration: none;
}

a:hover.lineno, a:active.lineno {
    color: #000; text-decoration: underline;
}

.line-empty, .line-precise {
    background-color: #dfd;
}

.line-imprecise {
    background-color: #ffa;
}

.line-any, .line-unanalyzed {
    background-color: #faa;
}
