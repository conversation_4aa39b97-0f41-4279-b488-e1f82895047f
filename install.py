#!/usr/bin/env python3
"""
Installation script for trading strategy dependencies.
Handles binary compatibility issues automatically.
"""

import subprocess
import sys
import os


def run_command(cmd, description="Running command"):
    """Run a command and return success status."""
    print(f"{description}...")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✓ Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False


def install_dependencies():
    """Install dependencies with compatibility fixes."""
    print("Trading Strategy Dependency Installation")
    print("=" * 50)
    
    # Step 1: Upgrade pip
    run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                "Upgrading pip")
    
    # Step 2: Uninstall potentially conflicting packages
    packages_to_remove = ["pandas", "numpy", "scipy"]
    for package in packages_to_remove:
        run_command([sys.executable, "-m", "pip", "uninstall", package, "-y"], 
                   f"Removing {package}")
    
    # Step 3: Clear pip cache
    run_command([sys.executable, "-m", "pip", "cache", "purge"], 
               "Clearing pip cache")
    
    # Step 4: Install packages in specific order
    packages = [
        "numpy==1.24.3",
        "pandas==2.0.3",
        "ta==0.10.2",
        "ccxt==4.4.94",  # Fixed: Use available version
        "optuna==3.4.0",
        "plotly==5.17.0",
        "tqdm==4.66.1"
    ]
    
    failed_packages = []
    for package in packages:
        if not run_command([sys.executable, "-m", "pip", "install", package], 
                          f"Installing {package}"):
            failed_packages.append(package)
    
    # Step 5: Test installation
    print("\nTesting installation...")
    try:
        import pandas as pd
        import numpy as np
        import ta
        import ccxt
        import optuna
        import plotly
        import tqdm
        
        # Test compatibility
        df = pd.DataFrame({'a': [1, 2, 3]})
        arr = np.array([1, 2, 3])
        
        print("✓ All packages imported successfully")
        print("✓ Binary compatibility test passed")
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False


def main():
    """Main installation function."""
    if install_dependencies():
        print("\n" + "=" * 50)
        print("✓ SUCCESS: Installation completed!")
        print("\nNext steps:")
        print("1. Test the installation: python test_simple.py")
        print("2. Run the strategy: python o3_simple.py")
    else:
        print("\n" + "=" * 50)
        print("✗ FAILED: Installation had issues")
        print("\nTry alternative methods:")
        print("1. Use conda: conda install pandas numpy")
        print("2. Use pre-compiled wheels: pip install --only-binary=all pandas numpy")


if __name__ == "__main__":
    main()
