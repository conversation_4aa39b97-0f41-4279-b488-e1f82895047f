#!/usr/bin/env python3
"""
Trading Strategy Optimization for BTC/USDT
Enhanced version with additional functionality and visualization.
"""

import ccxt
import pandas as pd
import numpy as np
import optuna
import plotly.graph_objects as go
import os
import json
import datetime
import time
from ta import trend, momentum, volatility
from tqdm import tqdm
from config_manager import config_manager


###############################################################################
# 1. Download free candles from Binance                                       #
###############################################################################
def fetch_binance(symbol=None, tf="15m", days=None):
    """
    Fetch OHLCV data from Binance exchange using configuration.

    Args:
        symbol: Trading pair symbol (uses config default if None)
        tf: Timeframe (e.g., "1m", "5m", "15m", "1h", "1d")
        days: Number of days of historical data to fetch (uses config default if None)

    Returns:
        DataFrame with OHLCV data indexed by timestamp

    Raises:
        ccxt.BaseError: If API request fails
    """
    # Use configuration defaults if not provided
    if symbol is None:
        symbol = config_manager.get("trading.symbol", "BTC/USDT")
    if days is None:
        days = config_manager.get("trading.days_of_data", 90)

    try:
        exch = ccxt.binance({"enableRateLimit": True})
        since = exch.milliseconds() - days * 24 * 60 * 60 * 1000
        bars = []
        ms_per_bar = exch.parse_timeframe(tf) * 1000

        while since < exch.milliseconds():
            batch = exch.fetch_ohlcv(symbol, tf, since=since, limit=1500)
            if not batch:
                break
            bars.extend(batch)
            since = batch[-1][0] + ms_per_bar

        if not bars:
            raise ValueError(f"No data fetched for {symbol} on {tf} timeframe")

        df = pd.DataFrame(
            bars, columns=["ts", "open", "high", "low", "close", "vol"]
        ).set_index("ts")
        df.index = pd.to_datetime(df.index, unit="ms")

        # Remove duplicates and sort by index
        df = df[~df.index.duplicated(keep='first')].sort_index()

        return df
    except Exception as e:
        raise RuntimeError(f"Failed to fetch data from Binance: {str(e)}") from e


###############################################################################
# 2. Indicators                                                               #
###############################################################################
def add_indicators(df, p):
    """
    Add technical indicators to the DataFrame.

    Args:
        df: DataFrame with OHLCV data
        p: Dictionary containing indicator parameters

    Returns:
        DataFrame with added technical indicators

    Raises:
        ValueError: If required parameters are missing or invalid
        KeyError: If required columns are missing from DataFrame
    """
    required_params = [
        "macd_fast", "macd_slow", "macd_signal", "rsi_len", "dmi_len",
        "trend_w_macd", "trend_w_rsi", "trend_w_dmi", "dyn_th", "strong_th"
    ]

    missing_params = [param for param in required_params if param not in p]
    if missing_params:
        raise ValueError(f"Missing required parameters: {missing_params}")

    required_columns = ["open", "high", "low", "close", "vol"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise KeyError(f"Missing required columns: {missing_columns}")

    if len(df) < max(p["macd_slow"], p["rsi_len"], p["dmi_len"]) + 10:
        raise ValueError("Insufficient data for indicator calculation")

    try:
        # MACD
        macd = trend.MACD(
            df.close,
            window_fast=int(p["macd_fast"]),
            window_slow=int(p["macd_slow"]),
            window_sign=int(p["macd_signal"])
        )
        df["macd_hist"] = macd.macd_diff()

        # RSI
        df["rsi"] = momentum.RSIIndicator(df.close, window=int(p["rsi_len"])).rsi()

        # DMI / ADX
        adx = trend.ADXIndicator(df.high, df.low, df.close, window=int(p["dmi_len"]))
        df["diplus"] = adx.adx_pos()
        df["diminus"] = adx.adx_neg()
        df["adx"] = adx.adx()

        # ATR (configurable window size)
        atr_window = config_manager.get("indicators.atr_window", 14)
        df["atr"] = volatility.AverageTrueRange(
            df.high, df.low, df.close, window=atr_window
        ).average_true_range()

        # Simple NWE proxy - handle division by zero
        ema_window = config_manager.get("indicators.ema_window", 50)
        ema_span = df.close.ewm(span=ema_window).mean()
        df["nwe_score"] = np.where(
            df.atr > 0,
            (df.close - ema_span) / df.atr,
            0
        )

        # Z-scores with proper error handling using configured fill values
        fill_values = config_manager.get("indicators.fill_na_values", {})
        df["macd_hist"] = df["macd_hist"].fillna(fill_values.get("macd_hist", 0.0))
        df["rsi"] = df["rsi"].fillna(fill_values.get("rsi", 50.0))
        df["diplus"] = df["diplus"].fillna(fill_values.get("diplus", 0.0))
        df["diminus"] = df["diminus"].fillna(fill_values.get("diminus", 0.0))
        df["atr"] = df["atr"].fillna(fill_values.get("atr_default", 1.0))

        z_macd = np.where(
            df.atr > 0,
            (df.macd_hist / df.atr).clip(-3, 3),
            0
        )
        z_rsi = ((df.rsi - 50) / 50).clip(-2, 2)
        z_dmi = ((df.diplus - df.diminus) / 100).clip(-2, 2)

        # Calculate composite trend score
        score = (
            z_macd * p["trend_w_macd"]
            + z_rsi * p["trend_w_rsi"]
            + z_dmi * p["trend_w_dmi"]
        )

        df["trend_score"] = 50 + (score / 3) * 50
        df["dyn_th"] = p["dyn_th"]
        df["strong_th"] = p["strong_th"]

        return df

    except Exception as e:
        raise RuntimeError(f"Failed to calculate indicators: {str(e)}") from e


###############################################################################
# 3. Signals & PnL                                                            #
###############################################################################
def generate_signals(df):
    """
    Generate trading signals based on trend score and thresholds.

    Args:
        df: DataFrame with trend_score, strong_th, and dyn_th columns

    Returns:
        DataFrame with added position and return columns

    Raises:
        KeyError: If required columns are missing
        ValueError: If DataFrame is empty or has insufficient data
    """
    required_columns = ["trend_score", "strong_th", "dyn_th", "close"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise KeyError(f"Missing required columns: {missing_columns}")

    if len(df) < 2:
        raise ValueError("Insufficient data for signal generation (need at least 2 rows)")

    try:
        # Generate entry signals
        long_en = (
            (df.trend_score.shift(1) < df.strong_th.shift(1)) &
            (df.trend_score >= df.strong_th)
        )
        short_en = (
            (df.trend_score.shift(1) > 100 - df.strong_th.shift(1)) &
            (df.trend_score <= 100 - df.strong_th)
        )

        # Generate exit signals
        exit_long = df.trend_score <= df.dyn_th
        exit_short = df.trend_score >= 100 - df.dyn_th

        # Initialize position array
        pos = np.zeros(len(df), dtype=np.int8)

        # Generate positions using vectorized operations where possible
        for i in range(1, len(df)):
            if pd.isna(long_en.iloc[i]) or pd.isna(short_en.iloc[i]):
                pos[i] = pos[i - 1]
                continue

            if long_en.iloc[i]:
                pos[i] = 1
            elif short_en.iloc[i]:
                pos[i] = -1
            else:
                pos[i] = pos[i - 1]

                # Apply exit conditions
                if pos[i] == 1 and not pd.isna(exit_long.iloc[i]) and exit_long.iloc[i]:
                    pos[i] = 0
                elif pos[i] == -1 and not pd.isna(exit_short.iloc[i]) and exit_short.iloc[i]:
                    pos[i] = 0

        # Add results to DataFrame
        df["pos"] = pos
        df["ret"] = df.close.pct_change().fillna(0)
        df["str_ret"] = df.pos.shift(1) * df.ret

        return df

    except Exception as e:
        raise RuntimeError(f"Failed to generate signals: {str(e)}") from e


def get_tf_minutes(tf):
    """
    Convert timeframe string to minutes.

    Args:
        tf: Timeframe string (e.g., "1m", "5m", "1h", "1d")

    Returns:
        Number of minutes in the timeframe

    Raises:
        ValueError: If timeframe format is not supported
    """
    if not isinstance(tf, str) or not tf:
        raise ValueError("Timeframe must be a non-empty string")

    tf = tf.lower().strip()

    if tf.endswith('m'):
        try:
            return int(tf[:-1])
        except ValueError:
            raise ValueError(f"Invalid minute timeframe format: {tf}")
    elif tf.endswith('h'):
        try:
            return int(tf[:-1]) * 60
        except ValueError:
            raise ValueError(f"Invalid hour timeframe format: {tf}")
    elif tf == '1d':
        return 1440
    else:
        raise ValueError(f"Unsupported timeframe: {tf}. Supported formats: Xm, Xh, 1d")

def metrics(df, tf):
    """
    Calculate trading strategy performance metrics.

    Args:
        df: DataFrame with str_ret column containing strategy returns
        tf: Timeframe string for annualization

    Returns:
        Dictionary with return, sharpe, and winrate metrics

    Raises:
        KeyError: If required columns are missing
        ValueError: If data is insufficient or invalid
    """
    if "str_ret" not in df.columns:
        raise KeyError("DataFrame must contain 'str_ret' column")

    if len(df) == 0:
        raise ValueError("DataFrame is empty")

    try:
        tf_min = get_tf_minutes(tf)
        bars_per_year = 365 * 24 * 60 / tf_min

        # Calculate cumulative return
        strategy_returns = df.str_ret.dropna()
        if len(strategy_returns) == 0:
            return {"return": 0.0, "sharpe": 0.0, "winrate": 0.0}

        cum_return = (1 + strategy_returns).prod() - 1

        # Calculate Sharpe ratio
        mean_return = strategy_returns.mean()
        std_return = strategy_returns.std()

        min_sharpe_threshold = config_manager.get("scoring.min_sharpe_threshold", 0.0)
        if std_return > 0 and not pd.isna(std_return):
            sharpe = np.sqrt(bars_per_year) * mean_return / std_return
            sharpe = max(sharpe, min_sharpe_threshold)  # Apply minimum threshold
        else:
            sharpe = min_sharpe_threshold

        # Calculate win rate
        winning_trades = (strategy_returns > 0).sum()
        total_trades = (strategy_returns != 0).sum()
        winrate = winning_trades / max(1, total_trades)

        return {
            "return": float(cum_return),
            "sharpe": float(sharpe),
            "winrate": float(winrate)
        }

    except Exception as e:
        raise RuntimeError(f"Failed to calculate metrics: {str(e)}") from e


###############################################################################
# 4. Objective for Optuna                                                     #
###############################################################################
def objective(trial, data_cache, timeframe):
    """
    Objective function for Optuna optimization using configuration.

    Args:
        trial: Optuna trial object
        data_cache: Dictionary containing DataFrames for each timeframe
        timeframe: Timeframe string to optimize for

    Returns:
        Composite score to maximize

    Raises:
        optuna.exceptions.TrialPruned: If trial should be pruned
        ValueError: If invalid parameters or insufficient data
    """
    if timeframe not in data_cache:
        raise ValueError(f"Timeframe {timeframe} not found in data cache")

    df = data_cache[timeframe].copy()

    min_data_points = config_manager.get("optimization.min_data_points", 100)
    if len(df) < min_data_points:
        raise ValueError(f"Insufficient data for {timeframe}: {len(df)} rows")

    # Use configured train/validation split
    train_split = config_manager.get("optimization.train_validation_split", 0.7)
    train_size = int(len(df) * train_split)

    min_train_size = config_manager.get("validation.min_train_size", 50)
    min_val_size = config_manager.get("validation.min_validation_size", 20)

    if train_size < min_train_size or len(df) - train_size < min_val_size:
        raise ValueError("Insufficient data for train/validation split")

    df_train = df.iloc[:train_size].copy()
    df_val = df.iloc[train_size:].copy()

    try:
        # Get parameter ranges for this timeframe from configuration
        ranges = config_manager.get_parameter_ranges_for_timeframe(timeframe)

        # Build parameter dictionary using configured ranges
        p = {
            "macd_fast": trial.suggest_int("macd_fast", ranges["macd"]["fast_min"], ranges["macd"]["fast_max"]),
            "macd_slow": trial.suggest_int("macd_slow", ranges["macd"]["slow_min"], ranges["macd"]["slow_max"]),
            "macd_signal": trial.suggest_int("macd_signal", ranges["macd"]["signal_min"], ranges["macd"]["signal_max"]),
            "rsi_len": trial.suggest_int("rsi_len", ranges["rsi"]["len_min"], ranges["rsi"]["len_max"]),
            "dmi_len": trial.suggest_int("dmi_len", ranges["dmi"]["len_min"], ranges["dmi"]["len_max"]),
            "trend_w_macd": trial.suggest_float("trend_w_macd", ranges["trend_weights"]["macd_min"], ranges["trend_weights"]["macd_max"]),
            "trend_w_rsi": trial.suggest_float("trend_w_rsi", ranges["trend_weights"]["rsi_min"], ranges["trend_weights"]["rsi_max"]),
            "trend_w_dmi": trial.suggest_float("trend_w_dmi", ranges["trend_weights"]["dmi_min"], ranges["trend_weights"]["dmi_max"]),
            "dyn_th": trial.suggest_float("dyn_th", ranges["thresholds"]["dynamic_min"], ranges["thresholds"]["dynamic_max"]),
            "strong_th": trial.suggest_float("strong_th", ranges["thresholds"]["strong_min"], ranges["thresholds"]["strong_max"]),
        }

        # Validate parameter constraints
        if p["macd_fast"] >= p["macd_slow"]:
            raise optuna.exceptions.TrialPruned()
        if p["dyn_th"] >= p["strong_th"]:
            raise optuna.exceptions.TrialPruned()

        # Evaluate on training set
        df_train = add_indicators(df_train, p)
        df_train = generate_signals(df_train)
        m_train = metrics(df_train, timeframe)

        # Early stopping: if training Sharpe is negative, prune the trial
        if m_train["sharpe"] <= 0:
            raise optuna.exceptions.TrialPruned()

        # Evaluate on validation set
        df_val = add_indicators(df_val, p)
        df_val = generate_signals(df_val)
        m_val = metrics(df_val, timeframe)

        # Store metrics for both training and validation sets
        trial.set_user_attr("metrics_train", m_train)
        trial.set_user_attr("metrics_val", m_val)

        # Composite score using configured weights
        weights = config_manager.get("scoring.composite_weights", {"sharpe": 0.6, "return": 0.3, "winrate": 0.1})
        return_cap = config_manager.get("scoring.return_cap", 3.0)
        winrate_center = config_manager.get("scoring.winrate_center", 0.5)
        winrate_scale = config_manager.get("scoring.winrate_scale", 10.0)

        composite_score = (
            weights["sharpe"] * m_val["sharpe"] +
            weights["return"] * min(m_val["return"] * 10, return_cap) +
            weights["winrate"] * (m_val["winrate"] - winrate_center) * winrate_scale
        )

        return float(composite_score)

    except optuna.exceptions.TrialPruned:
        raise
    except Exception as e:
        # Log the error and prune the trial
        trial.set_user_attr("error", str(e))
        raise optuna.exceptions.TrialPruned()


###############################################################################
# 5. Main                                                                     #
###############################################################################
def main():
    """Main function to run strategy optimization using configuration."""
    start_time = time.time()

    # Print configuration summary
    config_manager.print_summary()

    try:
        # Get timeframes and other settings from configuration
        tfs = config_manager.get_timeframes()
        symbol = config_manager.get("trading.symbol")
        days = config_manager.get("trading.days_of_data")
        min_data_points = config_manager.get("optimization.min_data_points")

        data_cache = {}

        print(f"\nPre-fetching {symbol} data for timeframes: {tfs}")
        for tf in tqdm(tfs, desc="Fetching data"):
            try:
                data_cache[tf] = fetch_binance(symbol=symbol, tf=tf, days=days)
                print(f"Fetched {len(data_cache[tf])} bars for {tf}")

                # Validate minimum data requirements
                if len(data_cache[tf]) < min_data_points:
                    print(f"Warning: {tf} has only {len(data_cache[tf])} data points, "
                          f"minimum recommended is {min_data_points}")

            except Exception as e:
                print(f"Failed to fetch data for {tf}: {str(e)}")
                continue

        if not data_cache:
            raise RuntimeError("Failed to fetch data for any timeframe")

        # Create results directory from configuration
        results_dir = config_manager.get("output.results_directory", "results")
        os.makedirs(results_dir, exist_ok=True)

        # Dictionary to store best parameters and metrics for each timeframe
        all_timeframe_results = {}

        # Get optimization settings from configuration
        n_trials = config_manager.get_n_trials()
        n_startup_trials = config_manager.get("optimization.n_startup_trials", 5)
        n_warmup_steps = config_manager.get("optimization.n_warmup_steps", 3)
        enable_pruning = config_manager.get("optimization.enable_pruning", True)
        show_progress = config_manager.get("performance.progress_bars", True)

        # Run optimization for each timeframe separately
        for tf in tfs:
            if tf not in data_cache:
                print(f"Skipping {tf} - no data available")
                continue

            print(f"\nRunning Bayesian optimization for {tf} timeframe...")
            tf_start_time = time.time()

            try:
                # Create a study with optional pruning
                study_kwargs = {"direction": "maximize"}
                if enable_pruning:
                    study_kwargs["pruner"] = optuna.pruners.MedianPruner(
                        n_startup_trials=n_startup_trials,
                        n_warmup_steps=n_warmup_steps
                    )

                study = optuna.create_study(**study_kwargs)

                # Run optimization for this timeframe
                study.optimize(
                    lambda t: objective(t, data_cache, tf),
                    n_trials=n_trials,
                    show_progress_bar=show_progress
                )

                tf_duration = time.time() - tf_start_time
                print(f"Optimization for {tf} completed with {n_trials} trials in {tf_duration:.1f}s")
                print(f"Best composite score for {tf}: {study.best_value:.4f}")

                # Get best parameters for this timeframe
                best_p = study.best_params
                # Back-test with best parameters
                df = data_cache[tf]
                df_bt = add_indicators(df.copy(), best_p)
                df_bt = generate_signals(df_bt)
                m = metrics(df_bt, tf)
                print(f"Back-test metrics for {tf}:", m)

                # Get parameter ranges used for this timeframe
                param_ranges = config_manager.get_parameter_ranges_for_timeframe(tf)

                # Store comprehensive results for this timeframe
                all_timeframe_results[tf] = {
                    "parameters": best_p,
                    "metrics": {
                        "return": m["return"],
                        "sharpe": m["sharpe"],
                        "winrate": m["winrate"]
                    },
                    "composite_score": study.best_value,
                    "optimization_metadata": {
                        "n_trials": n_trials,
                        "n_completed_trials": len(study.trials),
                        "n_pruned_trials": len([t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED]),
                        "optimization_duration_seconds": tf_duration,
                        "best_trial_number": study.best_trial.number,
                        "convergence_info": {
                            "best_value_history": [t.value for t in study.trials if t.value is not None][-10:],  # Last 10 values
                            "parameter_ranges_used": param_ranges
                        }
                    },
                    "data_info": {
                        "total_bars": len(data_cache[tf]),
                        "train_bars": int(len(data_cache[tf]) * config_manager.get("optimization.train_validation_split", 0.7)),
                        "validation_bars": len(data_cache[tf]) - int(len(data_cache[tf]) * config_manager.get("optimization.train_validation_split", 0.7)),
                        "date_range": {
                            "start": str(data_cache[tf].index[0]),
                            "end": str(data_cache[tf].index[-1])
                        }
                    }
                }

            except Exception as e:
                print(f"Optimization failed for {tf}: {str(e)}")
                continue

        if not all_timeframe_results:
            raise RuntimeError("No successful optimizations completed")

        # Calculate total optimization time
        total_duration = time.time() - start_time

        # Save all results to a consolidated file using configuration
        timestamp_format = config_manager.get("output.timestamp_format", "%Y%m%d_%H%M%S")
        timestamp = datetime.datetime.now().strftime(timestamp_format)

        consolidated_prefix = config_manager.get("output.consolidated_prefix", "all_timeframes_best_strategies")
        consolidated_file = os.path.join(results_dir, f"{consolidated_prefix}_{timestamp}.json")

        # Prepare comprehensive consolidated results dictionary
        consolidated_results = {
            "metadata": {
                "timestamp": timestamp,
                "total_optimization_duration_seconds": total_duration,
                "optimization_start_time": datetime.datetime.fromtimestamp(start_time).isoformat(),
                "optimization_end_time": datetime.datetime.now().isoformat(),
                "configuration_used": config_manager.config,  # Include full configuration
                "system_info": {
                    "timeframes_optimized": list(all_timeframe_results.keys()),
                    "total_timeframes_attempted": len(tfs),
                    "successful_optimizations": len(all_timeframe_results),
                    "failed_optimizations": len(tfs) - len(all_timeframe_results)
                }
            },
            "results": all_timeframe_results
        }

        # Save to file
        with open(consolidated_file, "w") as f:
            json.dump(consolidated_results, f, indent=4)

        print(f"\nAll timeframe results saved to: {consolidated_file}")

        # Print a summary table of results
        print("\n" + "="*80)
        print("SUMMARY OF BEST PARAMETERS FOR EACH TIMEFRAME")
        print("="*80)
        print(f"{'Timeframe':<8} {'Return':<10} {'Sharpe':<10} {'Win Rate':<10} {'Score':<10}")
        print("-"*80)

        for tf in tfs:
            if tf in all_timeframe_results:
                results = all_timeframe_results[tf]
                print(f"{tf:<8} {results['metrics']['return']*100:8.2f}% "
                      f"{results['metrics']['sharpe']:8.2f} "
                      f"{results['metrics']['winrate']*100:8.2f}% "
                      f"{results['composite_score']:8.2f}")

        print("="*80)

        # Analyze and explain the best parameters for each timeframe
        print("\n" + "="*80)
        print("DETAILED ANALYSIS OF BEST PARAMETERS FOR EACH TIMEFRAME")
        print("="*80)

        analyze_timeframe_parameters(all_timeframe_results, list(all_timeframe_results.keys()))

        # Find the best performing timeframe based on composite score
        best_tf = max(all_timeframe_results, key=lambda tf: all_timeframe_results[tf]["composite_score"])
        best_score = all_timeframe_results[best_tf]["composite_score"]
        print(f"\nBest overall timeframe: {best_tf} with composite score: {best_score:.4f}")

        # Ask user which timeframe to visualize
        available_tfs = list(all_timeframe_results.keys())
        print("\nVisualize strategy for which timeframe?")
        for i, tf in enumerate(available_tfs):
            print(f"{i+1}. {tf}")
        print(f"{len(available_tfs)+1}. Best timeframe ({best_tf})")
        print(f"{len(available_tfs)+2}. All timeframes (multiple plots)")

        try:
            choice = int(input(f"\nEnter your choice (1-{len(available_tfs)+2}): "))

            if choice == len(available_tfs)+2:
                # Plot all timeframes
                print("\nGenerating plots for all timeframes...")
                for tf in available_tfs:
                    if tf in data_cache:
                        print(f"Plotting {tf} timeframe...")
                        plot_strategy(tf, all_timeframe_results[tf]["parameters"],
                                     data_cache[tf], all_timeframe_results[tf]["metrics"])
            elif choice == len(available_tfs)+1:
                # Plot best timeframe
                print(f"\nPlotting best timeframe ({best_tf})...")
                plot_strategy(best_tf, all_timeframe_results[best_tf]["parameters"],
                             data_cache[best_tf], all_timeframe_results[best_tf]["metrics"])
            elif 1 <= choice <= len(available_tfs):
                # Plot selected timeframe
                selected_tf = available_tfs[choice-1]
                print(f"\nPlotting {selected_tf} timeframe...")
                plot_strategy(selected_tf, all_timeframe_results[selected_tf]["parameters"],
                             data_cache[selected_tf], all_timeframe_results[selected_tf]["metrics"])
            else:
                print("Invalid choice. Plotting best timeframe by default.")
                plot_strategy(best_tf, all_timeframe_results[best_tf]["parameters"],
                             data_cache[best_tf], all_timeframe_results[best_tf]["metrics"])
        except ValueError:
            print("Invalid input. Plotting best timeframe by default.")
            plot_strategy(best_tf, all_timeframe_results[best_tf]["parameters"],
                         data_cache[best_tf], all_timeframe_results[best_tf]["metrics"])
        except Exception as e:
            print(f"Error during visualization: {str(e)}")

        # Return the consolidated results for further processing if needed
        return all_timeframe_results

    except Exception as e:
        print(f"Error in main function: {str(e)}")
        raise RuntimeError(f"Optimization failed: {str(e)}") from e


###############################################################################
# 6. Analysis and Visualization                                               #
###############################################################################

def analyze_timeframe_parameters(results, timeframes):
    """
    Analyze and explain the best parameters for each timeframe
    
    Args:
        results: Dictionary containing the best parameters and metrics for each timeframe
        timeframes: List of timeframes to analyze
    """
    # Convert timeframes to minutes for easier comparison
    tf_minutes = {tf: get_tf_minutes(tf) for tf in timeframes}
    
    # Group timeframes into categories
    short_tfs = [tf for tf in timeframes if tf_minutes[tf] <= 15]
    medium_tfs = [tf for tf in timeframes if 15 < tf_minutes[tf] <= 60]
    long_tfs = [tf for tf in timeframes if 60 < tf_minutes[tf] <= 240]
    very_long_tfs = [tf for tf in timeframes if tf_minutes[tf] > 240]
    
    # Print general observations about parameter trends across timeframes
    print("\nGENERAL OBSERVATIONS ACROSS TIMEFRAMES:")
    print("-" * 80)
    
    # Analyze MACD parameters
    macd_fast_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "macd_fast")
    macd_slow_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "macd_slow")
    macd_signal_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "macd_signal")
    
    print(f"MACD Parameters:")
    print(f"  - Fast Period: {macd_fast_trend}")
    print(f"  - Slow Period: {macd_slow_trend}")
    print(f"  - Signal Period: {macd_signal_trend}")
    print(f"  - Explanation: MACD is a trend-following momentum indicator that shows the relationship")
    print(f"    between two moving averages of a security's price. The optimal MACD parameters")
    print(f"    typically increase with timeframe length to filter out noise in longer timeframes.")
    
    # Analyze RSI parameters
    rsi_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "rsi_len")
    print(f"\nRSI Parameters:")
    print(f"  - RSI Period: {rsi_trend}")
    print(f"  - Explanation: RSI measures the speed and change of price movements. Shorter RSI")
    print(f"    periods are more sensitive to price changes and can generate more signals,")
    print(f"    while longer periods smooth out the indicator and reduce false signals.")
    
    # Analyze DMI parameters
    dmi_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "dmi_len")
    print(f"\nDMI Parameters:")
    print(f"  - DMI Period: {dmi_trend}")
    print(f"  - Explanation: DMI helps determine if a security is trending and its direction.")
    print(f"    Longer DMI periods are less sensitive to price changes and better for identifying")
    print(f"    stronger, more established trends in longer timeframes.")
    
    # Analyze weight parameters
    macd_weight_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "trend_w_macd")
    rsi_weight_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "trend_w_rsi")
    dmi_weight_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "trend_w_dmi")
    
    print(f"\nIndicator Weights:")
    print(f"  - MACD Weight: {macd_weight_trend}")
    print(f"  - RSI Weight: {rsi_weight_trend}")
    print(f"  - DMI Weight: {dmi_weight_trend}")
    print(f"  - Explanation: The weights determine how much each indicator contributes to the")
    print(f"    overall trend score. Higher weights for an indicator mean it has more influence")
    print(f"    on trading decisions. The optimal weights vary by timeframe as different")
    print(f"    indicators perform better at different time scales.")
    
    # Analyze threshold parameters
    dyn_th_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "dyn_th")
    strong_th_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "strong_th")
    
    print(f"\nThreshold Parameters:")
    print(f"  - Dynamic Threshold: {dyn_th_trend}")
    print(f"  - Strong Threshold: {strong_th_trend}")
    print(f"  - Explanation: The dynamic threshold controls when to exit positions, while the")
    print(f"    strong threshold determines when to enter positions. Higher thresholds mean")
    print(f"    more conservative trading with fewer but potentially higher quality signals.")
    
    # Print detailed recommendations for each timeframe category
    print("\nRECOMMENDATIONS BY TIMEFRAME CATEGORY:")
    print("-" * 80)
    
    if short_tfs:
        print("\nShort Timeframes (≤ 15 minutes):")
        print("  - Best for: Scalping, short-term trading, capturing small price movements")
        print("  - Recommended parameters:")
        print("    * Shorter MACD periods to capture quick price movements")
        print("    * Shorter RSI and DMI periods for increased sensitivity")
        print("    * Higher weights for RSI to capture overbought/oversold conditions")
        print("    * Lower thresholds for more frequent trading opportunities")
        print("  - Timeframes in this category:", ", ".join(short_tfs))
    
    if medium_tfs:
        print("\nMedium Timeframes (15-60 minutes):")
        print("  - Best for: Intraday trading, swing trading on shorter time horizons")
        print("  - Recommended parameters:")
        print("    * Balanced MACD, RSI, and DMI periods")
        print("    * More balanced weights between indicators")
        print("    * Moderate thresholds for a balance of signal quality and quantity")
        print("  - Timeframes in this category:", ", ".join(medium_tfs))
    
    if long_tfs:
        print("\nLong Timeframes (1-4 hours):")
        print("  - Best for: Swing trading, position trading on medium time horizons")
        print("  - Recommended parameters:")
        print("    * Longer MACD periods to filter out market noise")
        print("    * Longer RSI and DMI periods for more reliable signals")
        print("    * Higher weights for MACD and DMI to capture stronger trends")
        print("    * Higher thresholds for higher quality signals")
        print("  - Timeframes in this category:", ", ".join(long_tfs))
    
    if very_long_tfs:
        print("\nVery Long Timeframes (> 4 hours):")
        print("  - Best for: Position trading, long-term investing")
        print("  - Recommended parameters:")
        print("    * Longest MACD, RSI, and DMI periods to identify major trends")
        print("    * Highest weights for DMI to capture strong, established trends")
        print("    * Highest thresholds for highest quality signals")
        print("  - Timeframes in this category:", ", ".join(very_long_tfs))
    
    # Print the best timeframe for different trading styles
    print("\nBEST TIMEFRAME RECOMMENDATIONS BY TRADING STYLE:")
    print("-" * 80)
    
    # Find best timeframe for each metric
    best_return_tf = max(timeframes, key=lambda tf: results[tf]["metrics"]["return"])
    best_sharpe_tf = max(timeframes, key=lambda tf: results[tf]["metrics"]["sharpe"])
    best_winrate_tf = max(timeframes, key=lambda tf: results[tf]["metrics"]["winrate"])
    best_overall_tf = max(timeframes, key=lambda tf: results[tf]["composite_score"])
    
    print(f"For maximum returns: {best_return_tf} (Return: {results[best_return_tf]['metrics']['return']*100:.2f}%)")
    print(f"For best risk-adjusted returns: {best_sharpe_tf} (Sharpe: {results[best_sharpe_tf]['metrics']['sharpe']:.2f})")
    print(f"For highest win rate: {best_winrate_tf} (Win Rate: {results[best_winrate_tf]['metrics']['winrate']*100:.2f}%)")
    print(f"For best overall performance: {best_overall_tf} (Composite Score: {results[best_overall_tf]['composite_score']:.2f})")
    
    print("\nFINAL RECOMMENDATIONS:")
    print("-" * 80)
    print(f"1. For scalping/day trading: Use {best_sharpe_tf if best_sharpe_tf in short_tfs + medium_tfs else short_tfs[0] if short_tfs else medium_tfs[0] if medium_tfs else timeframes[0]} timeframe")
    print(f"2. For swing trading: Use {best_sharpe_tf if best_sharpe_tf in medium_tfs + long_tfs else medium_tfs[0] if medium_tfs else long_tfs[0] if long_tfs else timeframes[0]} timeframe")
    print(f"3. For position trading: Use {best_sharpe_tf if best_sharpe_tf in long_tfs + very_long_tfs else long_tfs[0] if long_tfs else very_long_tfs[0] if very_long_tfs else timeframes[-1]} timeframe")
    print(f"4. For best overall performance: Use {best_overall_tf} timeframe")

def analyze_parameter_trend(results, timeframes, tf_minutes, param_name):
    """
    Analyze how a parameter changes across different timeframes
    
    Args:
        results: Dictionary containing the best parameters for each timeframe
        timeframes: List of timeframes to analyze
        tf_minutes: Dictionary mapping timeframes to their length in minutes
        param_name: Name of the parameter to analyze
    
    Returns:
        String describing the trend
    """
    # Sort timeframes by length in minutes
    sorted_tfs = sorted(timeframes, key=lambda tf: tf_minutes[tf])
    
    # Get parameter values for each timeframe
    values = [results[tf]["parameters"][param_name] for tf in sorted_tfs]
    
    # Check if there's a clear trend (removed unused variables)
    
    # Calculate correlation with timeframe length
    x = [tf_minutes[tf] for tf in sorted_tfs]
    y = values
    
    if len(x) > 1:  # Need at least 2 points for correlation
        correlation = np.corrcoef(x, y)[0, 1]
        
        if correlation > 0.7:
            return f"Strong positive correlation with timeframe length (correlation: {correlation:.2f})"
        elif correlation > 0.3:
            return f"Moderate positive correlation with timeframe length (correlation: {correlation:.2f})"
        elif correlation < -0.7:
            return f"Strong negative correlation with timeframe length (correlation: {correlation:.2f})"
        elif correlation < -0.3:
            return f"Moderate negative correlation with timeframe length (correlation: {correlation:.2f})"
        else:
            return f"No clear correlation with timeframe length (correlation: {correlation:.2f})"
    else:
        return "Not enough data points to determine trend"
def plot_strategy(tf, params, df, m):
    """
    Create a plot for a specific timeframe strategy
    
    Args:
        tf: Timeframe string (e.g., "1m", "1h")
        params: Dictionary of parameters for this timeframe
        df: DataFrame with price data
        m: Dictionary with metrics (return, sharpe, winrate)
    """
    # Prepare data for plotting
    df_bt = add_indicators(df.copy(), params)
    df_bt = generate_signals(df_bt)
    
    candles = go.Candlestick(
        x=df_bt.index,
        open=df_bt.open,
        high=df_bt.high,
        low=df_bt.low,
        close=df_bt.close,
        name="BTC/USDT",
    )
    
    # Create colored bars for trend score visualization
    colors = []
    for i in range(len(df_bt)):
        if df_bt.trend_score.iloc[i] >= df_bt.strong_th.iloc[i]:
            colors.append('rgba(0, 255, 0, 0.3)')  # Strong bullish - green
        elif df_bt.trend_score.iloc[i] <= (100 - df_bt.strong_th.iloc[i]):
            colors.append('rgba(255, 0, 0, 0.3)')  # Strong bearish - red
        elif df_bt.trend_score.iloc[i] > df_bt.dyn_th.iloc[i] and df_bt.trend_score.iloc[i] < df_bt.strong_th.iloc[i]:
            colors.append('rgba(144, 238, 144, 0.3)')  # Moderate bullish - light green
        elif df_bt.trend_score.iloc[i] < (100 - df_bt.dyn_th.iloc[i]) and df_bt.trend_score.iloc[i] > (100 - df_bt.strong_th.iloc[i]):
            colors.append('rgba(255, 160, 122, 0.3)')  # Moderate bearish - light red
        else:
            colors.append('rgba(211, 211, 211, 0.3)')  # Neutral - light gray
    
    # Add colored bars to visualize trend score zones
    bar_chart = go.Bar(
        x=df_bt.index,
        y=[100] * len(df_bt),  # Full height bars
        marker_color=colors,
        opacity=0.5,
        name="Trend Zones",
        yaxis="y2",
    )
    
    score = go.Scatter(
        x=df_bt.index,
        y=df_bt.trend_score,
        line=dict(color="dodgerblue", width=2),
        name="Trend-Score",
        yaxis="y2",
    )
    
    # Add horizontal lines for thresholds
    strong_th_line = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[df_bt.strong_th.iloc[0], df_bt.strong_th.iloc[0]],
        mode='lines',
        line=dict(color='green', width=1, dash='dash'),
        name=f"Strong Bullish Threshold ({df_bt.strong_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    strong_th_line_bear = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[100 - df_bt.strong_th.iloc[0], 100 - df_bt.strong_th.iloc[0]],
        mode='lines',
        line=dict(color='red', width=1, dash='dash'),
        name=f"Strong Bearish Threshold ({100 - df_bt.strong_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    dyn_th_line = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[df_bt.dyn_th.iloc[0], df_bt.dyn_th.iloc[0]],
        mode='lines',
        line=dict(color='green', width=1, dash='dot'),
        name=f"Exit Long Threshold ({df_bt.dyn_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    dyn_th_line_bear = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[100 - df_bt.dyn_th.iloc[0], 100 - df_bt.dyn_th.iloc[0]],
        mode='lines',
        line=dict(color='red', width=1, dash='dot'),
        name=f"Exit Short Threshold ({100 - df_bt.dyn_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    long_sig = go.Scatter(
        x=df_bt.index[df_bt.pos == 1],
        y=df_bt.low[df_bt.pos == 1] * 0.995,
        mode="markers",
        marker_symbol="triangle-up",
        marker_color="green",
        marker_size=10,
        name="Long",
    )
    
    short_sig = go.Scatter(
        x=df_bt.index[df_bt.pos == -1],
        y=df_bt.high[df_bt.pos == -1] * 1.005,
        mode="markers",
        marker_symbol="triangle-down",
        marker_color="red",
        marker_size=10,
        name="Short",
    )

    fig = go.Figure([bar_chart, candles, score, strong_th_line, strong_th_line_bear, 
                    dyn_th_line, dyn_th_line_bear, long_sig, short_sig])
    fig.update_layout(
        title=f"BTC/USDT {tf} – Sharpe {m['sharpe']:.2f}, Return {m['return']*100:.1f}%, Win Rate {m['winrate']*100:.1f}%",
        xaxis_rangeslider_visible=False,
        yaxis=dict(title="Price"),
        yaxis2=dict(title="Trend Score", overlaying="y", side="right", range=[0, 100]),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(t=100),
        height=800,
        annotations=[
            dict(
                x=0.5,
                y=1.05,
                xref="paper",
                yref="paper",
                text=f"Best Parameters: MACD({params['macd_fast']},{params['macd_slow']},{params['macd_signal']}), RSI({params['rsi_len']}), DMI({params['dmi_len']}), Weights({params['trend_w_macd']:.2f},{params['trend_w_rsi']:.2f},{params['trend_w_dmi']:.2f})",
                showarrow=False,
                font=dict(size=12)
            )
        ]
    )
    
    # Add a color legend for the bar chart
    fig.add_annotation(
        x=0.01,
        y=0.99,
        xref="paper",
        yref="paper",
        text="<b>Color Legend:</b><br>Green: Strong Bullish<br>Light Green: Moderate Bullish<br>Gray: Neutral<br>Light Red: Moderate Bearish<br>Red: Strong Bearish",
        showarrow=False,
        align="left",
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="black",
        borderwidth=1,
        font=dict(size=10)
    )
    fig.show()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user. Exiting gracefully...")
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\nOptimization process completed.")