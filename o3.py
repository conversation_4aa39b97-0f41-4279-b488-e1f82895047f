#!/usr/bin/env python3
"""
Back-test & optimise a simplified port of
   '[BrandonJames] Algo Beta' for BTC/USDT bars across multiple timeframes.

This enhanced version optimizes parameters for each timeframe separately and
provides detailed analysis of the best settings for each timeframe. Key features:

1. Optimizes trading strategy parameters for each timeframe (1m, 5m, 15m, 30m, 1h, 4h, 1d)
2. Adjusts parameter search spaces based on timeframe characteristics
3. Provides detailed analysis of parameter trends across timeframes
4. Offers recommendations for different trading styles and timeframe categories
5. Allows visualization of strategy performance for any timeframe
6. Saves consolidated results with best parameters for all timeframes

author : o3 – OpenAI    github.com/openai
date   : 2025-07-16
"""

import ccxt, pandas as pd, numpy as np, optuna, plotly.graph_objects as go
import os, json, datetime
from ta import trend, momentum, volatility
from tqdm import tqdm


###############################################################################
# 1. Download free candles from Binance                                       #
###############################################################################
def fetch_binance(symbol="BTC/USDT", tf="15m", days=90):
    exch = ccxt.binance({"enableRateLimit": True})
    since = exch.milliseconds() - days * 24 * 60 * 60 * 1000
    bars = []
    ms_per_bar = exch.parse_timeframe(tf) * 1000
    while since < exch.milliseconds():
        batch = exch.fetch_ohlcv(symbol, tf, since=since, limit=1500)
        if not batch:
            break
        bars.extend(batch)
        since = batch[-1][0] + ms_per_bar
    df = pd.DataFrame(
        bars, columns=["ts", "open", "high", "low", "close", "vol"]
    ).set_index("ts")
    df.index = pd.to_datetime(df.index, unit="ms")
    return df


###############################################################################
# 2. Indicators                                                               #
###############################################################################
def add_indicators(df: pd.DataFrame, p: dict) -> pd.DataFrame:
    # MACD
    macd = trend.MACD(
        df.close, window_fast=p["macd_fast"], window_slow=p["macd_slow"], window_sign=p["macd_signal"]
    )
    df["macd_hist"] = macd.macd_diff()

    # RSI
    df["rsi"] = momentum.RSIIndicator(df.close, window=p["rsi_len"]).rsi()

    # DMI / ADX
    adx = trend.ADXIndicator(df.high, df.low, df.close, window=p["dmi_len"])
    df["diplus"] = adx.adx_pos()
    df["diminus"] = adx.adx_neg()
    df["adx"] = adx.adx()

    # ATR
    df["atr"] = volatility.AverageTrueRange(df.high, df.low, df.close, window=14).average_true_range()

    # simple NWE proxy
    df["nwe_score"] = (df.close - df.close.ewm(span=50).mean()) / df.atr

    # z-scores
    z_macd = (df.macd_hist / df.atr).clip(-3, 3)
    z_rsi = ((df.rsi - 50) / 50).clip(-2, 2)
    z_dmi = ((df.diplus - df.diminus) / 100).clip(-2, 2)

    score = (
        z_macd * p["trend_w_macd"]
        + z_rsi * p["trend_w_rsi"]
        + z_dmi * p["trend_w_dmi"]
    )

    df["trend_score"] = 50 + (score / 3) * 50
    df["dyn_th"] = p["dyn_th"]
    df["strong_th"] = p["strong_th"]
    return df


###############################################################################
# 3. Signals & PnL                                                            #
###############################################################################
def generate_signals(df):
    long_en = (df.trend_score.shift(1) < df.strong_th.shift(1)) & (df.trend_score >= df.strong_th)
    short_en = (df.trend_score.shift(1) > 100 - df.strong_th.shift(1)) & (df.trend_score <= 100 - df.strong_th)

    exit_long = df.trend_score <= df.dyn_th
    exit_short = df.trend_score >= 100 - df.dyn_th

    pos = np.zeros(len(df))
    for i in range(1, len(df)):
        if long_en.iloc[i]:
            pos[i] = 1
        elif short_en.iloc[i]:
            pos[i] = -1
        else:
            pos[i] = pos[i - 1]
            if pos[i] == 1 and exit_long.iloc[i]:
                pos[i] = 0
            if pos[i] == -1 and exit_short.iloc[i]:
                pos[i] = 0

    df["pos"] = pos
    df["ret"] = df.close.pct_change().fillna(0)
    df["str_ret"] = df.pos.shift(1) * df.ret
    return df


def get_tf_minutes(tf):
    if tf.endswith('m'):
        return int(tf[:-1])
    elif tf.endswith('h'):
        return int(tf[:-1]) * 60
    elif tf == '1d':
        return 1440
    else:
        raise ValueError(f"Unsupported timeframe: {tf}")

def metrics(df, tf):
    tf_min = get_tf_minutes(tf)
    bars_per_year = 365 * 24 * 60 / tf_min
    cum = (1 + df.str_ret).cumprod().iloc[-1] - 1
    sharpe = np.sqrt(bars_per_year) * df.str_ret.mean() / df.str_ret.std() if df.str_ret.std() != 0 else 0
    win = (df.str_ret > 0).sum() / max(1, (df.str_ret != 0).sum())
    return {"return": cum, "sharpe": sharpe, "winrate": win}


###############################################################################
# 4. Objective for Optuna                                                     #
###############################################################################
def objective(trial, data_cache, timeframe):
    # Use the specified timeframe instead of suggesting it
    tf = timeframe
    df = data_cache[tf]
    
    # Use 70% of data for training, 30% for validation
    train_size = int(len(df) * 0.7)
    df_train = df.iloc[:train_size].copy()
    df_val = df.iloc[train_size:].copy()
    
    # Parameter search spaces optimized for each timeframe
    # Adjust ranges based on timeframe characteristics
    tf_minutes = get_tf_minutes(tf)
    
    # For shorter timeframes, use shorter indicator periods
    # For longer timeframes, use longer indicator periods
    macd_fast_min = max(5, int(5 * (tf_minutes / 15) ** 0.3))
    macd_fast_max = max(24, int(24 * (tf_minutes / 15) ** 0.3))
    macd_slow_min = max(25, int(25 * (tf_minutes / 15) ** 0.3))
    macd_slow_max = max(50, int(50 * (tf_minutes / 15) ** 0.3))
    macd_signal_min = max(5, int(5 * (tf_minutes / 15) ** 0.3))
    macd_signal_max = max(20, int(20 * (tf_minutes / 15) ** 0.3))
    rsi_min = max(6, int(6 * (tf_minutes / 15) ** 0.3))
    rsi_max = max(30, int(30 * (tf_minutes / 15) ** 0.3))
    dmi_min = max(6, int(6 * (tf_minutes / 15) ** 0.3))
    dmi_max = max(30, int(30 * (tf_minutes / 15) ** 0.3))
    
    p = {
        "macd_fast": trial.suggest_int("macd_fast", macd_fast_min, macd_fast_max),
        "macd_slow": trial.suggest_int("macd_slow", macd_slow_min, macd_slow_max),
        "macd_signal": trial.suggest_int("macd_signal", macd_signal_min, macd_signal_max),
        "rsi_len": trial.suggest_int("rsi_len", rsi_min, rsi_max),
        "dmi_len": trial.suggest_int("dmi_len", dmi_min, dmi_max),
        "trend_w_macd": trial.suggest_float("trend_w_macd", 0.2, 4.0),
        "trend_w_rsi": trial.suggest_float("trend_w_rsi", 0.2, 4.0),
        "trend_w_dmi": trial.suggest_float("trend_w_dmi", 0.2, 4.0),
        "dyn_th": trial.suggest_float("dyn_th", 50, 65),
        "strong_th": trial.suggest_float("strong_th", 60, 90),
    }

    # Evaluate on training set
    df_train = add_indicators(df_train, p)
    df_train = generate_signals(df_train)
    m_train = metrics(df_train, tf)
    
    # Early stopping: if training Sharpe is negative, prune the trial
    if m_train["sharpe"] <= 0:
        raise optuna.exceptions.TrialPruned()
    
    # Evaluate on validation set
    df_val = add_indicators(df_val, p)
    df_val = generate_signals(df_val)
    m_val = metrics(df_val, tf)
    
    # Store metrics for both training and validation sets
    trial.set_user_attr("metrics_train", m_train)
    trial.set_user_attr("metrics_val", m_val)
    
    # Composite score that balances Sharpe ratio, returns, and win rate
    # Sharpe is most important, but we also want good returns and win rate
    composite_score = (
        0.6 * m_val["sharpe"] + 
        0.3 * min(m_val["return"] * 10, 3) +  # Cap return contribution
        0.1 * (m_val["winrate"] - 0.5) * 10   # Normalize win rate around 0.5
    )
    
    return composite_score  # Maximize composite score


###############################################################################
# 5. Main                                                                     #
###############################################################################
def main():
    tfs = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
    data_cache = {}
    print("Pre-fetching data for all timeframes …")
    for tf in tqdm(tfs):
        data_cache[tf] = fetch_binance(tf=tf, days=90)
        print(f"Fetched {len(data_cache[tf])} bars for {tf}")

    # Create results directory if it doesn't exist
    results_dir = "results"
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    # Dictionary to store best parameters and metrics for each timeframe
    all_timeframe_results = {}
    
    # Run optimization for each timeframe separately
    for tf in tfs:
        print(f"\nRunning Bayesian optimization for {tf} timeframe...")
        
        # Create a study with pruning enabled for faster optimization
        study = optuna.create_study(
            direction="maximize",
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=3)
        )
        
        # Number of trials per timeframe (reduced to make total runtime reasonable)
        n_trials = 50
        
        # Run optimization for this timeframe
        study.optimize(
            lambda t: objective(t, data_cache, tf), 
            n_trials=n_trials, 
            show_progress_bar=True
        )
        
        print(f"Optimization for {tf} completed with {n_trials} trials")
        print(f"Best composite score for {tf}: {study.best_value:.4f}")
        
        # Get best parameters for this timeframe
        best_p = study.best_params
        print(f"Best parameters for {tf}:\n", best_p)
        
        # Back-test with best parameters
        df = data_cache[tf]
        df_bt = add_indicators(df.copy(), best_p)
        df_bt = generate_signals(df_bt)
        m = metrics(df_bt, tf)
        print(f"Back-test metrics for {tf}:", m)
        
        # Store results for this timeframe
        all_timeframe_results[tf] = {
            "parameters": best_p,
            "metrics": {
                "return": m["return"],
                "sharpe": m["sharpe"],
                "winrate": m["winrate"]
            },
            "composite_score": study.best_value
        }
    
    # Save all results to a consolidated file
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    consolidated_file = os.path.join(results_dir, f"all_timeframes_best_strategies_{timestamp}.json")
    
    # Prepare consolidated results dictionary
    consolidated_results = {
        "timestamp": timestamp,
        "timeframes": all_timeframe_results
    }
    
    # Save to file
    with open(consolidated_file, "w") as f:
        json.dump(consolidated_results, f, indent=4)
    
    print(f"\nAll timeframe results saved to: {consolidated_file}")
    
    # Print a summary table of results
    print("\n" + "="*80)
    print("SUMMARY OF BEST PARAMETERS FOR EACH TIMEFRAME")
    print("="*80)
    print(f"{'Timeframe':<8} {'Return':<10} {'Sharpe':<10} {'Win Rate':<10} {'Score':<10}")
    print("-"*80)
    
    for tf in tfs:
        results = all_timeframe_results[tf]
        print(f"{tf:<8} {results['metrics']['return']*100:8.2f}% {results['metrics']['sharpe']:8.2f} {results['metrics']['winrate']*100:8.2f}% {results['composite_score']:8.2f}")
    
    print("="*80)
    
    # Analyze and explain the best parameters for each timeframe
    print("\n" + "="*80)
    print("DETAILED ANALYSIS OF BEST PARAMETERS FOR EACH TIMEFRAME")
    print("="*80)
    
    analyze_timeframe_parameters(all_timeframe_results, tfs)
    
    # Find the best performing timeframe based on composite score
    best_tf = max(all_timeframe_results, key=lambda tf: all_timeframe_results[tf]["composite_score"])
    best_score = all_timeframe_results[best_tf]["composite_score"]
    print(f"\nBest overall timeframe: {best_tf} with composite score: {best_score:.4f}")
    
    # Ask user which timeframe to visualize
    print("\nVisualize strategy for which timeframe?")
    for i, tf in enumerate(tfs):
        print(f"{i+1}. {tf}")
    print(f"{len(tfs)+1}. Best timeframe ({best_tf})")
    print(f"{len(tfs)+2}. All timeframes (multiple plots)")
    
    try:
        choice = int(input("\nEnter your choice (1-{}): ".format(len(tfs)+2)))
        
        if choice == len(tfs)+2:
            # Plot all timeframes
            print("\nGenerating plots for all timeframes...")
            for tf in tfs:
                print(f"Plotting {tf} timeframe...")
                plot_strategy(tf, all_timeframe_results[tf]["parameters"], 
                             data_cache[tf], all_timeframe_results[tf]["metrics"])
        elif choice == len(tfs)+1:
            # Plot best timeframe
            print(f"\nPlotting best timeframe ({best_tf})...")
            plot_strategy(best_tf, all_timeframe_results[best_tf]["parameters"], 
                         data_cache[best_tf], all_timeframe_results[best_tf]["metrics"])
        elif 1 <= choice <= len(tfs):
            # Plot selected timeframe
            selected_tf = tfs[choice-1]
            print(f"\nPlotting {selected_tf} timeframe...")
            plot_strategy(selected_tf, all_timeframe_results[selected_tf]["parameters"], 
                         data_cache[selected_tf], all_timeframe_results[selected_tf]["metrics"])
        else:
            print("Invalid choice. Plotting best timeframe by default.")
            plot_strategy(best_tf, all_timeframe_results[best_tf]["parameters"], 
                         data_cache[best_tf], all_timeframe_results[best_tf]["metrics"])
    except ValueError:
        print("Invalid input. Plotting best timeframe by default.")
        plot_strategy(best_tf, all_timeframe_results[best_tf]["parameters"], 
                     data_cache[best_tf], all_timeframe_results[best_tf]["metrics"])
    
    # Return the consolidated results for further processing if needed
    return all_timeframe_results


###############################################################################
# 6. Analysis and Visualization                                               #
###############################################################################

def analyze_timeframe_parameters(results, timeframes):
    """
    Analyze and explain the best parameters for each timeframe
    
    Args:
        results: Dictionary containing the best parameters and metrics for each timeframe
        timeframes: List of timeframes to analyze
    """
    # Convert timeframes to minutes for easier comparison
    tf_minutes = {tf: get_tf_minutes(tf) for tf in timeframes}
    
    # Group timeframes into categories
    short_tfs = [tf for tf in timeframes if tf_minutes[tf] <= 15]
    medium_tfs = [tf for tf in timeframes if 15 < tf_minutes[tf] <= 60]
    long_tfs = [tf for tf in timeframes if 60 < tf_minutes[tf] <= 240]
    very_long_tfs = [tf for tf in timeframes if tf_minutes[tf] > 240]
    
    # Print general observations about parameter trends across timeframes
    print("\nGENERAL OBSERVATIONS ACROSS TIMEFRAMES:")
    print("-" * 80)
    
    # Analyze MACD parameters
    macd_fast_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "macd_fast")
    macd_slow_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "macd_slow")
    macd_signal_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "macd_signal")
    
    print(f"MACD Parameters:")
    print(f"  - Fast Period: {macd_fast_trend}")
    print(f"  - Slow Period: {macd_slow_trend}")
    print(f"  - Signal Period: {macd_signal_trend}")
    print(f"  - Explanation: MACD is a trend-following momentum indicator that shows the relationship")
    print(f"    between two moving averages of a security's price. The optimal MACD parameters")
    print(f"    typically increase with timeframe length to filter out noise in longer timeframes.")
    
    # Analyze RSI parameters
    rsi_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "rsi_len")
    print(f"\nRSI Parameters:")
    print(f"  - RSI Period: {rsi_trend}")
    print(f"  - Explanation: RSI measures the speed and change of price movements. Shorter RSI")
    print(f"    periods are more sensitive to price changes and can generate more signals,")
    print(f"    while longer periods smooth out the indicator and reduce false signals.")
    
    # Analyze DMI parameters
    dmi_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "dmi_len")
    print(f"\nDMI Parameters:")
    print(f"  - DMI Period: {dmi_trend}")
    print(f"  - Explanation: DMI helps determine if a security is trending and its direction.")
    print(f"    Longer DMI periods are less sensitive to price changes and better for identifying")
    print(f"    stronger, more established trends in longer timeframes.")
    
    # Analyze weight parameters
    macd_weight_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "trend_w_macd")
    rsi_weight_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "trend_w_rsi")
    dmi_weight_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "trend_w_dmi")
    
    print(f"\nIndicator Weights:")
    print(f"  - MACD Weight: {macd_weight_trend}")
    print(f"  - RSI Weight: {rsi_weight_trend}")
    print(f"  - DMI Weight: {dmi_weight_trend}")
    print(f"  - Explanation: The weights determine how much each indicator contributes to the")
    print(f"    overall trend score. Higher weights for an indicator mean it has more influence")
    print(f"    on trading decisions. The optimal weights vary by timeframe as different")
    print(f"    indicators perform better at different time scales.")
    
    # Analyze threshold parameters
    dyn_th_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "dyn_th")
    strong_th_trend = analyze_parameter_trend(results, timeframes, tf_minutes, "strong_th")
    
    print(f"\nThreshold Parameters:")
    print(f"  - Dynamic Threshold: {dyn_th_trend}")
    print(f"  - Strong Threshold: {strong_th_trend}")
    print(f"  - Explanation: The dynamic threshold controls when to exit positions, while the")
    print(f"    strong threshold determines when to enter positions. Higher thresholds mean")
    print(f"    more conservative trading with fewer but potentially higher quality signals.")
    
    # Print detailed recommendations for each timeframe category
    print("\nRECOMMENDATIONS BY TIMEFRAME CATEGORY:")
    print("-" * 80)
    
    if short_tfs:
        print("\nShort Timeframes (≤ 15 minutes):")
        print("  - Best for: Scalping, short-term trading, capturing small price movements")
        print("  - Recommended parameters:")
        print("    * Shorter MACD periods to capture quick price movements")
        print("    * Shorter RSI and DMI periods for increased sensitivity")
        print("    * Higher weights for RSI to capture overbought/oversold conditions")
        print("    * Lower thresholds for more frequent trading opportunities")
        print("  - Timeframes in this category:", ", ".join(short_tfs))
    
    if medium_tfs:
        print("\nMedium Timeframes (15-60 minutes):")
        print("  - Best for: Intraday trading, swing trading on shorter time horizons")
        print("  - Recommended parameters:")
        print("    * Balanced MACD, RSI, and DMI periods")
        print("    * More balanced weights between indicators")
        print("    * Moderate thresholds for a balance of signal quality and quantity")
        print("  - Timeframes in this category:", ", ".join(medium_tfs))
    
    if long_tfs:
        print("\nLong Timeframes (1-4 hours):")
        print("  - Best for: Swing trading, position trading on medium time horizons")
        print("  - Recommended parameters:")
        print("    * Longer MACD periods to filter out market noise")
        print("    * Longer RSI and DMI periods for more reliable signals")
        print("    * Higher weights for MACD and DMI to capture stronger trends")
        print("    * Higher thresholds for higher quality signals")
        print("  - Timeframes in this category:", ", ".join(long_tfs))
    
    if very_long_tfs:
        print("\nVery Long Timeframes (> 4 hours):")
        print("  - Best for: Position trading, long-term investing")
        print("  - Recommended parameters:")
        print("    * Longest MACD, RSI, and DMI periods to identify major trends")
        print("    * Highest weights for DMI to capture strong, established trends")
        print("    * Highest thresholds for highest quality signals")
        print("  - Timeframes in this category:", ", ".join(very_long_tfs))
    
    # Print the best timeframe for different trading styles
    print("\nBEST TIMEFRAME RECOMMENDATIONS BY TRADING STYLE:")
    print("-" * 80)
    
    # Find best timeframe for each metric
    best_return_tf = max(timeframes, key=lambda tf: results[tf]["metrics"]["return"])
    best_sharpe_tf = max(timeframes, key=lambda tf: results[tf]["metrics"]["sharpe"])
    best_winrate_tf = max(timeframes, key=lambda tf: results[tf]["metrics"]["winrate"])
    best_overall_tf = max(timeframes, key=lambda tf: results[tf]["composite_score"])
    
    print(f"For maximum returns: {best_return_tf} (Return: {results[best_return_tf]['metrics']['return']*100:.2f}%)")
    print(f"For best risk-adjusted returns: {best_sharpe_tf} (Sharpe: {results[best_sharpe_tf]['metrics']['sharpe']:.2f})")
    print(f"For highest win rate: {best_winrate_tf} (Win Rate: {results[best_winrate_tf]['metrics']['winrate']*100:.2f}%)")
    print(f"For best overall performance: {best_overall_tf} (Composite Score: {results[best_overall_tf]['composite_score']:.2f})")
    
    print("\nFINAL RECOMMENDATIONS:")
    print("-" * 80)
    print(f"1. For scalping/day trading: Use {best_sharpe_tf if best_sharpe_tf in short_tfs + medium_tfs else short_tfs[0] if short_tfs else medium_tfs[0] if medium_tfs else timeframes[0]} timeframe")
    print(f"2. For swing trading: Use {best_sharpe_tf if best_sharpe_tf in medium_tfs + long_tfs else medium_tfs[0] if medium_tfs else long_tfs[0] if long_tfs else timeframes[0]} timeframe")
    print(f"3. For position trading: Use {best_sharpe_tf if best_sharpe_tf in long_tfs + very_long_tfs else long_tfs[0] if long_tfs else very_long_tfs[0] if very_long_tfs else timeframes[-1]} timeframe")
    print(f"4. For best overall performance: Use {best_overall_tf} timeframe")

def analyze_parameter_trend(results, timeframes, tf_minutes, param_name):
    """
    Analyze how a parameter changes across different timeframes
    
    Args:
        results: Dictionary containing the best parameters for each timeframe
        timeframes: List of timeframes to analyze
        tf_minutes: Dictionary mapping timeframes to their length in minutes
        param_name: Name of the parameter to analyze
    
    Returns:
        String describing the trend
    """
    # Sort timeframes by length in minutes
    sorted_tfs = sorted(timeframes, key=lambda tf: tf_minutes[tf])
    
    # Get parameter values for each timeframe
    values = [results[tf]["parameters"][param_name] for tf in sorted_tfs]
    
    # Check if there's a clear trend
    increasing = all(values[i] <= values[i+1] for i in range(len(values)-1))
    decreasing = all(values[i] >= values[i+1] for i in range(len(values)-1))
    
    # Calculate correlation with timeframe length
    x = [tf_minutes[tf] for tf in sorted_tfs]
    y = values
    
    if len(x) > 1:  # Need at least 2 points for correlation
        correlation = np.corrcoef(x, y)[0, 1]
        
        if correlation > 0.7:
            return f"Strong positive correlation with timeframe length (correlation: {correlation:.2f})"
        elif correlation > 0.3:
            return f"Moderate positive correlation with timeframe length (correlation: {correlation:.2f})"
        elif correlation < -0.7:
            return f"Strong negative correlation with timeframe length (correlation: {correlation:.2f})"
        elif correlation < -0.3:
            return f"Moderate negative correlation with timeframe length (correlation: {correlation:.2f})"
        else:
            return f"No clear correlation with timeframe length (correlation: {correlation:.2f})"
    else:
        return "Not enough data points to determine trend"
def plot_strategy(tf, params, df, m):
    """
    Create a plot for a specific timeframe strategy
    
    Args:
        tf: Timeframe string (e.g., "1m", "1h")
        params: Dictionary of parameters for this timeframe
        df: DataFrame with price data
        m: Dictionary with metrics (return, sharpe, winrate)
    """
    # Prepare data for plotting
    df_bt = add_indicators(df.copy(), params)
    df_bt = generate_signals(df_bt)
    
    candles = go.Candlestick(
        x=df_bt.index,
        open=df_bt.open,
        high=df_bt.high,
        low=df_bt.low,
        close=df_bt.close,
        name="BTC/USDT",
    )
    
    # Create colored bars for trend score visualization
    colors = []
    for i in range(len(df_bt)):
        if df_bt.trend_score.iloc[i] >= df_bt.strong_th.iloc[i]:
            colors.append('rgba(0, 255, 0, 0.3)')  # Strong bullish - green
        elif df_bt.trend_score.iloc[i] <= (100 - df_bt.strong_th.iloc[i]):
            colors.append('rgba(255, 0, 0, 0.3)')  # Strong bearish - red
        elif df_bt.trend_score.iloc[i] > df_bt.dyn_th.iloc[i] and df_bt.trend_score.iloc[i] < df_bt.strong_th.iloc[i]:
            colors.append('rgba(144, 238, 144, 0.3)')  # Moderate bullish - light green
        elif df_bt.trend_score.iloc[i] < (100 - df_bt.dyn_th.iloc[i]) and df_bt.trend_score.iloc[i] > (100 - df_bt.strong_th.iloc[i]):
            colors.append('rgba(255, 160, 122, 0.3)')  # Moderate bearish - light red
        else:
            colors.append('rgba(211, 211, 211, 0.3)')  # Neutral - light gray
    
    # Add colored bars to visualize trend score zones
    bar_chart = go.Bar(
        x=df_bt.index,
        y=[100] * len(df_bt),  # Full height bars
        marker_color=colors,
        opacity=0.5,
        name="Trend Zones",
        yaxis="y2",
    )
    
    score = go.Scatter(
        x=df_bt.index,
        y=df_bt.trend_score,
        line=dict(color="dodgerblue", width=2),
        name="Trend-Score",
        yaxis="y2",
    )
    
    # Add horizontal lines for thresholds
    strong_th_line = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[df_bt.strong_th.iloc[0], df_bt.strong_th.iloc[0]],
        mode='lines',
        line=dict(color='green', width=1, dash='dash'),
        name=f"Strong Bullish Threshold ({df_bt.strong_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    strong_th_line_bear = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[100 - df_bt.strong_th.iloc[0], 100 - df_bt.strong_th.iloc[0]],
        mode='lines',
        line=dict(color='red', width=1, dash='dash'),
        name=f"Strong Bearish Threshold ({100 - df_bt.strong_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    dyn_th_line = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[df_bt.dyn_th.iloc[0], df_bt.dyn_th.iloc[0]],
        mode='lines',
        line=dict(color='green', width=1, dash='dot'),
        name=f"Exit Long Threshold ({df_bt.dyn_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    dyn_th_line_bear = go.Scatter(
        x=[df_bt.index[0], df_bt.index[-1]],
        y=[100 - df_bt.dyn_th.iloc[0], 100 - df_bt.dyn_th.iloc[0]],
        mode='lines',
        line=dict(color='red', width=1, dash='dot'),
        name=f"Exit Short Threshold ({100 - df_bt.dyn_th.iloc[0]:.1f})",
        yaxis="y2",
    )
    
    long_sig = go.Scatter(
        x=df_bt.index[df_bt.pos == 1],
        y=df_bt.low[df_bt.pos == 1] * 0.995,
        mode="markers",
        marker_symbol="triangle-up",
        marker_color="green",
        marker_size=10,
        name="Long",
    )
    
    short_sig = go.Scatter(
        x=df_bt.index[df_bt.pos == -1],
        y=df_bt.high[df_bt.pos == -1] * 1.005,
        mode="markers",
        marker_symbol="triangle-down",
        marker_color="red",
        marker_size=10,
        name="Short",
    )

    fig = go.Figure([bar_chart, candles, score, strong_th_line, strong_th_line_bear, 
                    dyn_th_line, dyn_th_line_bear, long_sig, short_sig])
    fig.update_layout(
        title=f"BTC/USDT {tf} – Sharpe {m['sharpe']:.2f}, Return {m['return']*100:.1f}%, Win Rate {m['winrate']*100:.1f}%",
        xaxis_rangeslider_visible=False,
        yaxis=dict(title="Price"),
        yaxis2=dict(title="Trend Score", overlaying="y", side="right", range=[0, 100]),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(t=100),
        height=800,
        annotations=[
            dict(
                x=0.5,
                y=1.05,
                xref="paper",
                yref="paper",
                text=f"Best Parameters: MACD({params['macd_fast']},{params['macd_slow']},{params['macd_signal']}), RSI({params['rsi_len']}), DMI({params['dmi_len']}), Weights({params['trend_w_macd']:.2f},{params['trend_w_rsi']:.2f},{params['trend_w_dmi']:.2f})",
                showarrow=False,
                font=dict(size=12)
            )
        ]
    )
    
    # Add a color legend for the bar chart
    fig.add_annotation(
        x=0.01,
        y=0.99,
        xref="paper",
        yref="paper",
        text="<b>Color Legend:</b><br>Green: Strong Bullish<br>Light Green: Moderate Bullish<br>Gray: Neutral<br>Light Red: Moderate Bearish<br>Red: Strong Bearish",
        showarrow=False,
        align="left",
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="black",
        borderwidth=1,
        font=dict(size=10)
    )
    fig.show()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user. Exiting gracefully...")
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\nOptimization process completed.")