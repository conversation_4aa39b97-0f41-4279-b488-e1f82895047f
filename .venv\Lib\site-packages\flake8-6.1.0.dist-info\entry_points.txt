[console_scripts]
flake8 = flake8.main.cli:main

[flake8.extension]
E = flake8.plugins.pycodestyle:pycodestyle_logical
F = flake8.plugins.pyflakes:FlakesChecker
W = flake8.plugins.pycodestyle:pycodestyle_physical

[flake8.report]
default = flake8.formatting.default:Default
pylint = flake8.formatting.default:Pylint
quiet-filename = flake8.formatting.default:FilenameOnly
quiet-nothing = flake8.formatting.default:Nothing
