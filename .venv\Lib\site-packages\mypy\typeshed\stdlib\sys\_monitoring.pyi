# This py312+ module provides annotations for `sys.monitoring`.
# It's named `sys._monitoring` in typeshed,
# because trying to import `sys.monitoring` will fail at runtime!
# At runtime, `sys.monitoring` has the unique status
# of being a `types.ModuleType` instance that cannot be directly imported,
# and exists in the `sys`-module namespace despite `sys` not being a package.

from collections.abc import Callable
from types import CodeType
from typing import Any

DEBUGGER_ID: int
COVERAGE_ID: int
PROFILER_ID: int
OPTIMIZER_ID: int

def use_tool_id(tool_id: int, name: str, /) -> None: ...
def free_tool_id(tool_id: int, /) -> None: ...
def get_tool(tool_id: int, /) -> str | None: ...

events: _events

class _events:
    BRANCH: int
    CALL: int
    C_RAISE: int
    C_RETURN: int
    EXCEPTION_HANDLED: int
    INSTRUCTION: int
    JUMP: int
    LINE: int
    NO_EVENTS: int
    PY_RESUME: int
    PY_RETURN: int
    PY_START: int
    PY_THROW: int
    PY_UNWIND: int
    PY_YIELD: int
    RAISE: int
    RERAISE: int
    STOP_ITERATION: int

def get_events(tool_id: int, /) -> int: ...
def set_events(tool_id: int, event_set: int, /) -> None: ...
def get_local_events(tool_id: int, code: CodeType, /) -> int: ...
def set_local_events(tool_id: int, code: CodeType, event_set: int, /) -> int: ...
def restart_events() -> None: ...

DISABLE: object
MISSING: object

def register_callback(tool_id: int, event: int, func: Callable[..., Any] | None, /) -> Callable[..., Any] | None: ...
