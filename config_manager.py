#!/usr/bin/env python3
"""
Configuration management system for trading strategy optimization.
Handles loading, validation, and fallback for configuration settings.
"""

import json
import os
from typing import Dict, Any, Optional
import copy


class ConfigManager:
    """Manages configuration loading and validation with fallback defaults."""
    
    # Default configuration as fallback
    DEFAULT_CONFIG = {
        "trading": {
            "symbol": "BTC/USDT",
            "days_of_data": 90,
            "timeframes": ["1h", "4h"],  # Conservative default
            "test_timeframes": ["1h", "4h"],
            "use_test_mode": True  # Safe default
        },
        "optimization": {
            "n_trials": 50,  # Conservative default
            "test_n_trials": 20,
            "n_startup_trials": 5,
            "n_warmup_steps": 3,
            "train_validation_split": 0.7,
            "min_data_points": 100,
            "timeout_per_trial": 30,
            "enable_pruning": True
        },
        "parameter_ranges": {
            "macd": {
                "fast_min_base": 8, "fast_max_base": 16,
                "slow_min_base": 20, "slow_max_base": 30,
                "signal_min_base": 6, "signal_max_base": 12,
                "timeframe_scaling": {"enabled": False, "scale_power": 0.3, "reference_timeframe_minutes": 15}
            },
            "rsi": {
                "len_min_base": 10, "len_max_base": 20,
                "timeframe_scaling": {"enabled": False, "scale_power": 0.3, "reference_timeframe_minutes": 15}
            },
            "dmi": {
                "len_min_base": 10, "len_max_base": 20,
                "timeframe_scaling": {"enabled": False, "scale_power": 0.3, "reference_timeframe_minutes": 15}
            },
            "trend_weights": {"macd_min": 0.5, "macd_max": 2.0, "rsi_min": 0.5, "rsi_max": 2.0, "dmi_min": 0.5, "dmi_max": 2.0},
            "thresholds": {"dynamic_min": 52.0, "dynamic_max": 62.0, "strong_min": 65.0, "strong_max": 85.0}
        },
        "indicators": {
            "atr_window": 14, "ema_window": 50,
            "fill_na_values": {"macd_hist": 0.0, "rsi": 50.0, "diplus": 0.0, "diminus": 0.0, "atr_default": 1.0}
        },
        "scoring": {
            "composite_weights": {"sharpe": 0.7, "return": 0.3, "winrate": 0.0},
            "return_cap": 3.0, "winrate_center": 0.5, "winrate_scale": 10.0, "min_sharpe_threshold": 0.0
        },
        "output": {
            "results_directory": "results", "logs_directory": "logs",
            "file_prefix": "optimization_results", "consolidated_prefix": "all_timeframes_best_strategies",
            "timestamp_format": "%Y%m%d_%H%M%S", "save_individual_timeframes": True,
            "save_consolidated": True, "include_metadata": True
        },
        "visualization": {
            "enabled": True, "default_choice": "best", "auto_open": False,
            "plot_format": "html", "include_volume": True, "include_indicators": True
        },
        "performance": {"parallel_optimization": False, "cache_data": True, "verbose_logging": True, "progress_bars": True},
        "validation": {
            "validate_parameters": True, "check_data_quality": True,
            "min_train_size": 50, "min_validation_size": 20, "max_missing_data_ratio": 0.05
        }
    }
    
    def __init__(self, config_file: str = "config.json"):
        """Initialize configuration manager."""
        self.config_file = config_file
        self.config = self._load_config()
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file with fallback to defaults."""
        if not os.path.exists(self.config_file):
            print(f"⚠ Config file '{self.config_file}' not found. Using default configuration.")
            return copy.deepcopy(self.DEFAULT_CONFIG)
        
        try:
            with open(self.config_file, 'r') as f:
                loaded_config = json.load(f)
            
            # Merge with defaults to ensure all keys exist
            config = self._merge_configs(self.DEFAULT_CONFIG, loaded_config)
            print(f"✓ Configuration loaded from '{self.config_file}'")
            return config
            
        except json.JSONDecodeError as e:
            print(f"✗ Error parsing '{self.config_file}': {e}")
            print("Using default configuration.")
            return copy.deepcopy(self.DEFAULT_CONFIG)
        except Exception as e:
            print(f"✗ Error loading '{self.config_file}': {e}")
            print("Using default configuration.")
            return copy.deepcopy(self.DEFAULT_CONFIG)
    
    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge loaded config with defaults."""
        result = copy.deepcopy(default)
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _validate_config(self) -> None:
        """Validate configuration values and fix common issues."""
        # Validate trading settings
        if self.config["trading"]["days_of_data"] < 1:
            print("⚠ Invalid days_of_data, setting to 30")
            self.config["trading"]["days_of_data"] = 30
        
        # Validate optimization settings
        if self.config["optimization"]["n_trials"] < 1:
            print("⚠ Invalid n_trials, setting to 20")
            self.config["optimization"]["n_trials"] = 20
        
        # Validate train/validation split
        split = self.config["optimization"]["train_validation_split"]
        if not (0.1 <= split <= 0.9):
            print("⚠ Invalid train_validation_split, setting to 0.7")
            self.config["optimization"]["train_validation_split"] = 0.7
        
        # Validate parameter ranges
        self._validate_parameter_ranges()
        
        # Validate scoring weights
        weights = self.config["scoring"]["composite_weights"]
        total_weight = sum(weights.values())
        if abs(total_weight - 1.0) > 0.01:  # Allow small floating point errors
            print(f"⚠ Composite weights sum to {total_weight:.3f}, normalizing to 1.0")
            for key in weights:
                weights[key] /= total_weight
    
    def _validate_parameter_ranges(self) -> None:
        """Validate parameter ranges for indicators."""
        ranges = self.config["parameter_ranges"]
        
        # Validate MACD ranges
        macd = ranges["macd"]
        if macd["fast_max_base"] <= macd["fast_min_base"]:
            print("⚠ Invalid MACD fast range, fixing")
            macd["fast_min_base"], macd["fast_max_base"] = 8, 16
        
        if macd["slow_min_base"] <= macd["fast_max_base"]:
            print("⚠ MACD slow min must be > fast max, fixing")
            macd["slow_min_base"] = macd["fast_max_base"] + 1
        
        # Validate thresholds
        thresh = ranges["thresholds"]
        if thresh["dynamic_min"] >= thresh["strong_min"]:
            print("⚠ Dynamic threshold must be < strong threshold, fixing")
            thresh["dynamic_min"] = 52.0
            thresh["strong_min"] = 65.0
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'trading.symbol')."""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_timeframes(self) -> list:
        """Get appropriate timeframes based on test mode setting."""
        if self.get("trading.use_test_mode", True):
            return self.get("trading.test_timeframes", ["1h"])
        else:
            return self.get("trading.timeframes", ["1h", "4h"])
    
    def get_n_trials(self) -> int:
        """Get appropriate number of trials based on test mode setting."""
        if self.get("trading.use_test_mode", True):
            return self.get("optimization.test_n_trials", 20)
        else:
            return self.get("optimization.n_trials", 50)
    
    def get_parameter_ranges_for_timeframe(self, timeframe: str) -> Dict[str, Any]:
        """Get parameter ranges adjusted for specific timeframe."""
        ranges = copy.deepcopy(self.config["parameter_ranges"])
        
        # Apply timeframe scaling if enabled
        tf_minutes = self._get_tf_minutes(timeframe)
        
        for indicator in ["macd", "rsi", "dmi"]:
            scaling_config = ranges[indicator].get("timeframe_scaling", {})
            if scaling_config.get("enabled", False):
                scale_factor = (tf_minutes / scaling_config["reference_timeframe_minutes"]) ** scaling_config["scale_power"]
                
                if indicator == "macd":
                    ranges[indicator]["fast_min"] = max(5, int(ranges[indicator]["fast_min_base"] * scale_factor))
                    ranges[indicator]["fast_max"] = max(24, int(ranges[indicator]["fast_max_base"] * scale_factor))
                    ranges[indicator]["slow_min"] = max(ranges[indicator]["fast_max"] + 1, int(ranges[indicator]["slow_min_base"] * scale_factor))
                    ranges[indicator]["slow_max"] = max(50, int(ranges[indicator]["slow_max_base"] * scale_factor))
                    ranges[indicator]["signal_min"] = max(5, int(ranges[indicator]["signal_min_base"] * scale_factor))
                    ranges[indicator]["signal_max"] = max(20, int(ranges[indicator]["signal_max_base"] * scale_factor))
                else:
                    ranges[indicator]["len_min"] = max(6, int(ranges[indicator]["len_min_base"] * scale_factor))
                    ranges[indicator]["len_max"] = max(30, int(ranges[indicator]["len_max_base"] * scale_factor))
            else:
                # Use base values without scaling
                if indicator == "macd":
                    ranges[indicator]["fast_min"] = ranges[indicator]["fast_min_base"]
                    ranges[indicator]["fast_max"] = ranges[indicator]["fast_max_base"]
                    ranges[indicator]["slow_min"] = ranges[indicator]["slow_min_base"]
                    ranges[indicator]["slow_max"] = ranges[indicator]["slow_max_base"]
                    ranges[indicator]["signal_min"] = ranges[indicator]["signal_min_base"]
                    ranges[indicator]["signal_max"] = ranges[indicator]["signal_max_base"]
                else:
                    ranges[indicator]["len_min"] = ranges[indicator]["len_min_base"]
                    ranges[indicator]["len_max"] = ranges[indicator]["len_max_base"]
        
        return ranges
    
    def _get_tf_minutes(self, tf: str) -> int:
        """Convert timeframe string to minutes."""
        if tf.endswith('m'):
            return int(tf[:-1])
        elif tf.endswith('h'):
            return int(tf[:-1]) * 60
        elif tf == '1d':
            return 1440
        else:
            return 60  # Default to 1 hour
    
    def save_config(self, filename: Optional[str] = None) -> None:
        """Save current configuration to file."""
        if filename is None:
            filename = self.config_file
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.config, f, indent=2)
            print(f"✓ Configuration saved to '{filename}'")
        except Exception as e:
            print(f"✗ Error saving configuration: {e}")
    
    def print_summary(self) -> None:
        """Print a summary of current configuration."""
        print("\n" + "="*60)
        print("CONFIGURATION SUMMARY")
        print("="*60)
        print(f"Symbol: {self.get('trading.symbol')}")
        print(f"Data period: {self.get('trading.days_of_data')} days")
        print(f"Timeframes: {self.get_timeframes()}")
        print(f"Trials per timeframe: {self.get_n_trials()}")
        print(f"Test mode: {self.get('trading.use_test_mode')}")
        print(f"Results directory: {self.get('output.results_directory')}")
        print("="*60)


# Global configuration instance
config_manager = ConfigManager()
