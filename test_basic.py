#!/usr/bin/env python3
"""
Basic tests that don't require pandas/numpy.
These tests verify the core structure and logic.
"""

import pytest
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_python_version():
    """Test that we're running a supported Python version."""
    version = sys.version_info
    assert version.major == 3, "Python 3 is required"
    assert version.minor >= 8, "Python 3.8+ is required"
    print(f"✓ Python {version.major}.{version.minor}.{version.micro}")


def test_file_structure():
    """Test that required files exist."""
    required_files = [
        'o3.py',
        'requirements.txt',
        'data_validator.py',
        'performance_metrics.py',
        'logging_config.py',
        'config.yaml',
        'IMPROVEMENTS_SUMMARY.md',
        'SETUP_GUIDE.md'
    ]
    
    for file in required_files:
        assert os.path.exists(file), f"Required file {file} is missing"
        print(f"✓ {file} exists")


def test_imports_without_dependencies():
    """Test basic Python imports that don't require external packages."""
    import json
    import os
    import datetime
    from typing import Dict, List, Optional
    
    # Test that we can create basic data structures
    config = {
        "test": True,
        "value": 42,
        "items": [1, 2, 3]
    }
    
    assert isinstance(config, dict)
    assert config["test"] is True
    assert len(config["items"]) == 3
    print("✓ Basic Python functionality works")


def test_config_file_structure():
    """Test that config.yaml has valid structure."""
    try:
        import yaml
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Check for required sections
        required_sections = ['data', 'optimization', 'indicators', 'scoring']
        for section in required_sections:
            assert section in config, f"Missing config section: {section}"
        
        print("✓ config.yaml structure is valid")
    except ImportError:
        print("⚠ PyYAML not installed, skipping config validation")
    except Exception as e:
        pytest.fail(f"Config file validation failed: {e}")


def test_requirements_file():
    """Test that requirements.txt is readable."""
    with open('requirements.txt', 'r') as f:
        content = f.read()
    
    # Check for key packages
    required_packages = ['pandas', 'numpy', 'ta', 'ccxt', 'optuna']
    for package in required_packages:
        assert package in content, f"Package {package} not found in requirements.txt"
    
    print("✓ requirements.txt contains required packages")


def test_o3_file_syntax():
    """Test that o3.py has valid Python syntax."""
    with open('o3.py', 'r') as f:
        content = f.read()
    
    try:
        compile(content, 'o3.py', 'exec')
        print("✓ o3.py has valid Python syntax")
    except SyntaxError as e:
        pytest.fail(f"Syntax error in o3.py: {e}")


def test_data_validator_syntax():
    """Test that data_validator.py has valid syntax."""
    with open('data_validator.py', 'r') as f:
        content = f.read()
    
    try:
        compile(content, 'data_validator.py', 'exec')
        print("✓ data_validator.py has valid Python syntax")
    except SyntaxError as e:
        pytest.fail(f"Syntax error in data_validator.py: {e}")


def test_performance_metrics_syntax():
    """Test that performance_metrics.py has valid syntax."""
    with open('performance_metrics.py', 'r') as f:
        content = f.read()
    
    try:
        compile(content, 'performance_metrics.py', 'exec')
        print("✓ performance_metrics.py has valid Python syntax")
    except SyntaxError as e:
        pytest.fail(f"Syntax error in performance_metrics.py: {e}")


def test_directory_structure():
    """Test that we can create required directories."""
    test_dirs = ['test_results', 'test_logs']
    
    for dir_name in test_dirs:
        os.makedirs(dir_name, exist_ok=True)
        assert os.path.exists(dir_name), f"Could not create directory {dir_name}"
        
        # Clean up
        os.rmdir(dir_name)
    
    print("✓ Directory creation works")


def test_json_operations():
    """Test JSON operations for results saving."""
    import json
    import tempfile
    
    test_data = {
        "timestamp": "2023-01-01_12:00:00",
        "timeframes": {
            "1h": {
                "parameters": {"test": 1},
                "metrics": {"return": 0.1, "sharpe": 1.5}
            }
        }
    }
    
    # Test writing and reading JSON
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_data, f, indent=4)
        temp_file = f.name
    
    try:
        with open(temp_file, 'r') as f:
            loaded_data = json.load(f)
        
        assert loaded_data == test_data, "JSON round-trip failed"
        print("✓ JSON operations work")
    finally:
        os.unlink(temp_file)


class TestBasicFunctionality:
    """Test basic functionality without external dependencies."""
    
    def test_timeframe_parsing_logic(self):
        """Test timeframe parsing logic without importing the main module."""
        # Simulate the get_tf_minutes function logic
        def get_tf_minutes_mock(tf):
            if tf.endswith('m'):
                return int(tf[:-1])
            elif tf.endswith('h'):
                return int(tf[:-1]) * 60
            elif tf == '1d':
                return 1440
            else:
                raise ValueError(f"Unsupported timeframe: {tf}")
        
        # Test cases
        test_cases = [
            ("1m", 1),
            ("5m", 5),
            ("15m", 15),
            ("1h", 60),
            ("4h", 240),
            ("1d", 1440)
        ]
        
        for tf, expected in test_cases:
            result = get_tf_minutes_mock(tf)
            assert result == expected, f"Expected {expected} for {tf}, got {result}"
        
        print("✓ Timeframe parsing logic works")
    
    def test_parameter_validation_logic(self):
        """Test parameter validation logic."""
        def validate_params_mock(params):
            errors = []
            
            if params.get("macd_fast", 0) >= params.get("macd_slow", 0):
                errors.append("MACD fast must be less than slow")
            
            if params.get("dyn_th", 0) >= params.get("strong_th", 0):
                errors.append("Dynamic threshold must be less than strong threshold")
            
            return len(errors) == 0, errors
        
        # Test valid parameters
        valid_params = {
            "macd_fast": 12,
            "macd_slow": 26,
            "dyn_th": 55,
            "strong_th": 75
        }
        
        is_valid, errors = validate_params_mock(valid_params)
        assert is_valid, f"Valid parameters failed validation: {errors}"
        
        # Test invalid parameters
        invalid_params = {
            "macd_fast": 26,
            "macd_slow": 12,  # Wrong order
            "dyn_th": 75,
            "strong_th": 55   # Wrong order
        }
        
        is_valid, errors = validate_params_mock(invalid_params)
        assert not is_valid, "Invalid parameters passed validation"
        assert len(errors) == 2, f"Expected 2 errors, got {len(errors)}"
        
        print("✓ Parameter validation logic works")


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "-s"])
